import { openai } from '@ai-sdk/openai';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { generateText } from 'ai';
import { getSupabaseClient } from '@/models/db';
import { saveNameGeneration } from '@/models/naming';
import type { GeneratedName, NameGeneration, GeneratorConfig } from '@/types/naming';
import type { GeneratorSeoContentData } from '@/types/seo-content';

/**
 * 获取AI模型实例，支持自定义baseURL
 */
export function getAIModel() {
  // 如果配置了自定义baseURL，使用createOpenAICompatible
  if (process.env.OPENAI_BASE_URL) {
    const customProvider = createOpenAICompatible({
      name: 'custom-openai',
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    });
    return customProvider('gpt-3.5-turbo');
  }
  
  // 否则使用默认的OpenAI
  return openai('gpt-3.5-turbo');
}

/**
 * A simple template engine to replace placeholders like {{key}}.
 */
function fillPromptTemplate(template: string, data: Record<string, any>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return data[key] || match;
  });
}

/**
 * AI名字生成主函数
 */
export async function generateNames(input: {
  user_uuid?: string;
  generator_type: string;
  user_prompt: string;
  parameters: Record<string, any>;
}): Promise<{ generation: NameGeneration; results: GeneratedName[] }> {
  try {
    // 1. 验证输入参数
    validateGenerateInput(input);

    // 2. 从数据库获取生成器配置
    const supabase = getSupabaseClient();
    const { data: config, error: configError } = await supabase
      .from('nomenus_generators')
      .select('*')
      .eq('type', input.generator_type)
      .single<GeneratorConfig>();

    if (configError || !config) {
      throw new Error(`Generator config for type "${input.generator_type}" not found.`);
    }

    // 3. 构建完整的AI prompt
    const allParams = { ...input.parameters, user_prompt: input.user_prompt, count: 4 };
    const fullPrompt = fillPromptTemplate(config.prompt_template, allParams);

    console.log('Generating names with prompt:', fullPrompt);

    // 4. 调用AI生成名字
    const { text } = await generateText({
      model: getAIModel(),
      prompt: fullPrompt,
      temperature: 0.8, // 增加一些创造性
      maxTokens: 2000,
    });

    console.log('AI response:', text);

    // 5. 解析AI返回的结果
    let results: GeneratedName[] = [];
    try {
      const parsed = JSON.parse(text);
      results = parsed.results || [];
    } catch (e) {
      console.error("Failed to parse AI response as JSON, falling back.", e);
      results = fallbackParseResponse(text);
    }
    
    // 6. 为结果注入上下文信息
    const resultsWithContext = results.map(r => ({
      ...r,
      gender: input.parameters.gender || 'neutral',
      elf_type: input.parameters.template || config.type,
    }));


    // 7. 验证生成结果
    validateNameResults(resultsWithContext);

    // 8. 保存生成记录到数据库
    const generation = await saveNameGeneration({
      user_uuid: input.user_uuid,
      generator_type: input.generator_type,
      user_prompt: input.user_prompt,
      parameters: input.parameters,
      results: resultsWithContext,
    });

    return { generation, results: resultsWithContext };
  } catch (error) {
    console.error('Error generating names:', error);
    throw new Error(
      error instanceof Error ? error.message : 'Failed to generate names'
    );
  }
}

/**
 * 解析AI返回的JSON响应
 */
export function parseAIResponse(text: string): GeneratedName[] {
  try {
    // 尝试提取JSON内容
    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (!jsonMatch) {
      throw new Error('No JSON array found in AI response');
    }

    const jsonStr = jsonMatch[0];
    const parsed = JSON.parse(jsonStr);

    if (!Array.isArray(parsed)) {
      throw new Error('AI response is not an array');
    }

    // 转换并验证每个名字对象
    return parsed.map((item, index) => {
      if (!item.name || !item.description) {
        throw new Error(`Invalid name object at index ${index}: missing name or description`);
      }

      return {
        name: String(item.name).trim(),
        description: String(item.description).trim(),
        pronunciation: item.pronunciation ? String(item.pronunciation).trim() : undefined,
        tags: item.tags || [],
      } as GeneratedName;
    });
  } catch (error) {
    console.error('Error parsing AI response:', error);
    console.error('Raw text:', text);

    // 降级处理：如果解析失败，尝试从文本中提取名字
    return fallbackParseResponse(text);
  }
}

/**
 * 降级解析方式：从文本中尝试提取名字信息
 */
function fallbackParseResponse(text: string): GeneratedName[] {
  const names: GeneratedName[] = [];
  const lines = text.split('\n');
  
  let currentName: Partial<GeneratedName> = {};
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    // 尝试匹配名字行（通常以数字开头或是加粗的）
    const nameMatch = trimmed.match(/^(?:\d+\.?\s*)?(?:\*\*)?([A-Za-z\s'-]+)(?:\*\*)?/);
    if (nameMatch && trimmed.length < 50) {
      // 保存上一个名字
      if (currentName.name) {
        names.push({
          name: currentName.name,
          description: currentName.description || 'A mystical name with ancient origins.',
          pronunciation: currentName.pronunciation,
          tags: [],
        });
      }
      
      // 开始新的名字
      currentName = { name: nameMatch[1].trim() };
    } else if (trimmed.length > 20 && !currentName.description) {
      // 这可能是描述
      currentName.description = trimmed;
    }
  }
  
  // 保存最后一个名字
  if (currentName.name) {
    names.push({
      name: currentName.name,
      description: currentName.description || 'A mystical name with ancient origins.',
      pronunciation: currentName.pronunciation,
      tags: [],
    });
  }
  
  // 如果还是没有解析出名字，返回默认结果
  if (names.length === 0) {
    names.push({
      name: 'Mystral',
      description: 'An enigmatic name that emerged from the mists of creation, carrying whispers of ancient magic and untold stories.',
      pronunciation: 'Mis-trail',
      tags: [],
    });
  }
  
  return names.slice(0, 4); // 最多返回4个名字
}

/**
 * 验证生成输入参数
 */
function validateGenerateInput(input: {
  generator_type: string;
  user_prompt: string;
  parameters: any;
}): void {
  if (!input.generator_type) {
    throw new Error('Generator type is required');
  }

  if (!input.user_prompt || input.user_prompt.trim().length === 0) {
    throw new Error('User prompt is required');
  }

  if (input.user_prompt.length > 1000) {
    throw new Error('User prompt is too long (max 1000 characters)');
  }
}

/**
 * 验证生成的名字结果
 */
function validateNameResults(results: GeneratedName[]): void {
  if (!Array.isArray(results) || results.length === 0) {
    throw new Error('No valid names were generated');
  }

  if (results.length > 10) {
    throw new Error('Too many names generated');
  }

  for (const [index, name] of results.entries()) {
    if (!name.name || typeof name.name !== 'string' || name.name.trim().length === 0) {
      throw new Error(`Invalid name at index ${index}: name is required`);
    }

    if (!name.description || typeof name.description !== 'string' || name.description.trim().length === 0) {
      throw new Error(`Invalid name at index ${index}: description is required`);
    }

    if (name.name.length > 100) {
      throw new Error(`Name at index ${index} is too long`);
    }

    if (name.description.length > 1000) {
      throw new Error(`Description at index ${index} is too long`);
    }
  }
}

/**
 * 检查AI服务可用性
 */
export async function checkAIServiceHealth(): Promise<{ status: 'ok' | 'error'; message: string }> {
  try {
    const { text } = await generateText({
      model: getAIModel(),
      prompt: 'Respond with just the word "healthy"',
      maxTokens: 10,
    });

    if (text.toLowerCase().includes('healthy')) {
      return { status: 'ok', message: 'AI service is healthy' };
    } else {
      return { status: 'error', message: 'AI service responded but not as expected' };
    }
  } catch (error) {
    return { 
      status: 'error', 
      message: `AI service error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * 批量生成名字（用于初始化示例数据）
 */
export async function generateBatchNames(
  generator_type: string,
  prompts: string[],
  parameters: any = {}
): Promise<GeneratedName[]> {
  const allResults: GeneratedName[] = [];

  for (const prompt of prompts) {
    try {
      const { results } = await generateNames({
        generator_type,
        user_prompt: prompt,
        parameters: { ...parameters, count: 2 }, // 每个prompt生成2个名字
      });
      allResults.push(...results);
      
      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Failed to generate names for prompt: ${prompt}`, error);
      // 继续处理其他prompts
    }
  }

  return allResults;
}



/**
 * 获取生成器SEO内容
 */
export async function getGeneratorSeoContent(
  generator_type: string,
  locale: string
): Promise<GeneratorSeoContentData | null> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }

    return await import(
      `@/i18n/pages/generators/${generator_type}.${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load ${generator_type}.${locale}.json, falling back to en.json`);

    try {
      return await import(`@/i18n/pages/generators/${generator_type}.en.json`).then(
        (module) => module.default
      );
    } catch (fallbackError) {
      console.warn(`Failed to load fallback ${generator_type}.en.json`);
      return null;
    }
  }
} 

export { getSavedNames, getGeneratorConfig, getAllGeneratorTypes } from '@/models/generators';

