"use client";

import { NavigationConfig } from "@/types/navigation";
import { NavigationMenuContent } from "@/components/ui/navigation-menu";
import CategorySection from "./CategorySection";
import { groupBy } from "@/lib/navigation-utils";

interface MegaMenuProps {
  config: NavigationConfig;
}

export default function MegaMenu({ config }: MegaMenuProps) {
  // 按分类组织生成器
  const generatorsByCategory = groupBy(config.generators, 'category');
  
  // 过滤出有生成器的分类
  const categoriesWithGenerators = config.categories.filter(
    category => generatorsByCategory[category.id]?.length > 0
  );

  if (categoriesWithGenerators.length === 0) {
    return (
      <NavigationMenuContent className="w-[400px] p-6">
        <div className="text-center text-muted-foreground">
          暂无可用的生成器
        </div>
      </NavigationMenuContent>
    );
  }

  // 根据分类数量决定列数和宽度
  const gridCols = categoriesWithGenerators.length >= 3 ? 'grid-cols-3' :
                   categoriesWithGenerators.length === 2 ? 'grid-cols-2' :
                   'grid-cols-1';

  const menuWidth = categoriesWithGenerators.length >= 3 ? 'min-w-[720px]' :
                    categoriesWithGenerators.length === 2 ? 'min-w-[520px]' :
                    'min-w-[360px]';

  return (
    <NavigationMenuContent className={`${menuWidth} max-w-[900px] w-auto p-4`}>
      <div className={`grid ${gridCols} gap-4 min-w-0`}>
        {categoriesWithGenerators.map(category => (
          <CategorySection
            key={category.id}
            category={category}
            generators={generatorsByCategory[category.id] || []}
          />
        ))}
      </div>
    </NavigationMenuContent>
  );
}
