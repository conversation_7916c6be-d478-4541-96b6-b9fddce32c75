"use client";

import { NavigationGenerator } from "@/types/navigation";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getBadgeVariant, getBadgeText } from "@/lib/navigation-utils";

interface GeneratorNavItemProps {
  generator: NavigationGenerator;
  variant?: 'mega' | 'mobile';
  className?: string;
}

export default function GeneratorNavItem({ 
  generator, 
  variant = 'mega',
  className 
}: GeneratorNavItemProps) {
  return (
    <Link
      href={generator.url as any}
      className={cn(
        "group flex items-start gap-2.5 p-2.5 rounded-md transition-all duration-200",
        "hover:bg-accent/50 hover:text-accent-foreground hover:shadow-sm",
        "focus:bg-accent/50 focus:text-accent-foreground focus:outline-none focus:ring-1 focus:ring-primary/20",
        variant === 'mobile' && "border-b border-border last:border-0 rounded-none",
        className
      )}
    >
      {/* 图标 */}
      <Icon
        name={generator.icon}
        className="w-4 h-4 text-primary shrink-0 mt-0.5 group-hover:scale-110 transition-transform"
      />

      <div className="flex-1 min-w-0">
        {/* 名称和角标 */}
        <div className="flex items-center gap-1.5 mb-0.5">
          <h4 className="font-medium text-sm text-foreground group-hover:text-accent-foreground truncate">
            {generator.name}
          </h4>
          {generator.badge && (
            <Badge
              variant={getBadgeVariant(generator.badge)}
              className="text-[10px] px-1 py-0 h-4 shrink-0"
            >
              {getBadgeText(generator.badge)}
            </Badge>
          )}
        </div>

        {/* 描述 */}
        <p className="text-[11px] text-muted-foreground leading-snug line-clamp-2">
          {generator.description.length > 55
            ? `${generator.description.substring(0, 55)}...`
            : generator.description}
        </p>
      </div>
    </Link>
  );
}
