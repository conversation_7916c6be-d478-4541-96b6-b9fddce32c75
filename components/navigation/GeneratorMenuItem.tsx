"use client";

import { useEffect, useState } from "react";
import { NavigationMenuItem, NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import Icon from "@/components/icon";
import MegaMenu from "./MegaMenu";
import { getNavigationConfig } from "@/config/navigation";
import { NavigationConfig } from "@/types/navigation";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";

export default function GeneratorMenuItem() {
  const t = useTranslations('header.generators');
  const [config, setConfig] = useState<NavigationConfig>({ categories: [], generators: [] });
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const locale = (params?.locale as string) || 'en';

  useEffect(() => {
    async function loadConfig() {
      try {
        const navigationConfig = await getNavigationConfig(locale);
        setConfig(navigationConfig);
      } catch (error) {
        console.error('Failed to load navigation config:', error);
      } finally {
        setLoading(false);
      }
    }

    loadConfig();
  }, [locale]);

  if (loading) {
    return (
      <NavigationMenuItem className="text-muted-foreground">
        <NavigationMenuTrigger>
          <Icon name="RiSparkling2Line" className="size-4 shrink-0 mr-2" />
          <span>{t('title')}</span>
        </NavigationMenuTrigger>
      </NavigationMenuItem>
    );
  }

  return (
    <NavigationMenuItem className="text-muted-foreground">
      <NavigationMenuTrigger>
        <Icon name="RiSparkling2Line" className="size-4 shrink-0 mr-2" />
        <span>{t('title')}</span>
      </NavigationMenuTrigger>
      <MegaMenu config={config} />
    </NavigationMenuItem>
  );
}
