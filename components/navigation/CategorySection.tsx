"use client";

import { NavigationCategory, NavigationGenerator } from "@/types/navigation";
import Icon from "@/components/icon";
import GeneratorNavItem from "./GeneratorNavItem";

interface CategorySectionProps {
  category: NavigationCategory;
  generators: NavigationGenerator[];
}

export default function CategorySection({ category, generators }: CategorySectionProps) {
  if (generators.length === 0) {
    return null;
  }

  return (
    <div className="space-y-2 min-w-0">
      {/* 分类标题 */}
      <div className="flex items-center gap-2 pb-1.5 mb-1 border-b border-border/60">
        <Icon name={category.icon} className="w-3.5 h-3.5 text-primary shrink-0" />
        <h3 className="font-semibold text-xs text-foreground/90 truncate uppercase tracking-wide">{category.name}</h3>
      </div>

      {/* 生成器列表 */}
      <div className="space-y-0.5">
        {generators.map(generator => (
          <GeneratorNavItem
            key={generator.id}
            generator={generator}
            variant="mega"
          />
        ))}
      </div>
    </div>
  );
}
