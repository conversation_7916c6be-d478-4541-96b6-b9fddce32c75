"use client";

import { CoreInteractionZone } from "@/components/core";
import GeneratorSeoContent from "@/components/generator/GeneratorSeoContent";
import HowToUseGuide from "@/components/seo/HowToUseGuide";
import Icon from "@/components/icon";
import type { CoreInteractionConfig, GeneratorCard } from "@/types/core-interaction";
import type { GeneratorSeoContentData } from '@/types/seo-content';
import { useTranslations } from "next-intl";

interface GeneratorPageWrapperProps {
  id: string;
  seoContent?: GeneratorSeoContentData | null;
  coreConfig: CoreInteractionConfig;
}

export default function GeneratorPageWrapper({
  id,
  seoContent,
  coreConfig
}: GeneratorPageWrapperProps) {
  const defaultGenerator = coreConfig.generators.find((g: GeneratorCard) => g.id === id);
  return (
    <div className="min-h-screen bg-background">
      {/* 页面标题区域 - 与核心区域风格统一 */}
      <section className="pt-24 pb-8">
        <div className="container">
          <div className="w-full max-w-4xl mx-auto">
            <div className="text-center mb-8">
              {/* <div className="inline-flex items-center gap-3 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 mb-6">
                {defaultGenerator?.icon && (
                  <Icon
                    name={defaultGenerator.icon}
                    className="w-5 h-5 text-primary"
                  />
                )}
                <span className="text-sm font-medium text-primary">{t("magical")}</span>
              </div> */}
              <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
                {defaultGenerator?.name}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                {defaultGenerator?.description}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 核心交互区域 */}
      <section className="pb-24">
        <div className="container">
          <div className="w-full max-w-4xl mx-auto space-y-6">
            <CoreInteractionZone
              config={coreConfig}
              initialState={{ selectedGenerator: defaultGenerator }} // 默认选中当前生成器
              onGenerate={async (config, prompt, generator, settings) => {
                // 直接调用新的API
                if (!generator) {
                  throw new Error(config.default_message.is_generator_selected);
                }

                if (!prompt.trim()) {
                  throw new Error(config.default_message.is_prompt_empty);
                }

                const response = await fetch(`/api/v2/generate-names`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    config: config,
                    generator_id: generator.id,
                    user_prompt: prompt.trim(),
                    settings: {
                      name_count: parseInt(settings.nameCount || '5'),
                      include_meaning: settings.includeMeaning || false,
                      name_length: settings.nameLength || 'medium',
                    }
                  })
                });

                if (!response.ok) {
                  const errorData = await response.json().catch(() => ({}));
                  throw new Error(errorData.error?.message || `API 请求失败: ${response.status}`);
                }

                const data = await response.json();
                
                if (!data.success) {
                  throw new Error(data.error?.message || config.default_message.generation_failed);
                }

                return data.data.results;
              }}
            />
          </div>
        </div>
      </section>

      {/* 使用指南区域 */}
      {seoContent?.seo_content?.how_to_use && (
        <section className="py-16">
          <div className="container">
            <div className="w-full max-w-4xl mx-auto">
              <HowToUseGuide
                generatorType={id}
                howToUseData={seoContent.seo_content.how_to_use}
              />
            </div>
          </div>
        </section>
      )}

      {/* SEO 内容区域 - 与核心区域风格统一 */}
      {seoContent && (
        <section className="py-16">
          <div className="container">
            <div className="w-full max-w-4xl mx-auto">
              <GeneratorSeoContent seoContent={seoContent} />
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
