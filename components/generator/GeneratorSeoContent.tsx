'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import type { GeneratorSeoContentData } from '@/types/seo-content';
interface SeoContentProps {
  seoContent: GeneratorSeoContentData;
}

// SEO 结果卡片组件 - 使用与核心区域相同的样式
function SeoResultCard({ item }: { item: any }) {
  const [isSaved, setIsSaved] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleSave = () => {
    setIsSaved(true);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(item.name);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  return (
    <Card
      className={cn(
        "group relative transition-all duration-300 cursor-pointer",
        "border border-border/50 bg-card/50 backdrop-blur-sm",
        "hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1",
        "hover:border-primary/30 hover:bg-card/80"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-6">
        {/* 顶部区域：名字 + 操作按钮 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            {/* 主名字 */}
            <h3 className="text-2xl font-bold text-foreground mb-1 tracking-wide">
              {item.name}
            </h3>

            {/* 发音 - 弱化处理 */}
            {item.pronunciation && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground/70">
                <svg className="w-3.5 h-3.5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M8.586 8.586A2 2 0 0011.414 5.414L15 9l-3.586 3.586A2 2 0 008.586 8.586z" />
                </svg>
                <span className="italic font-light">{item.pronunciation}</span>
              </div>
            )}
          </div>

          {/* 操作按钮 - 始终显示但透明度变化 */}
          <div className={cn(
            "flex gap-2 transition-opacity duration-200",
            isHovered ? "opacity-100" : "opacity-30"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className={cn(
                "h-8 w-8 p-0 rounded-full transition-all duration-200",
                "hover:bg-primary/10 hover:text-primary",
                copySuccess && "bg-green-100 text-green-600"
              )}
              title="复制名字"
            >
              {copySuccess ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              disabled={isSaved}
              className={cn(
                "h-8 w-8 p-0 rounded-full transition-all duration-200",
                isSaved
                  ? "bg-red-100 text-red-500 hover:bg-red-100 hover:text-red-500"
                  : "hover:bg-primary/10 hover:text-primary"
              )}
              title={isSaved ? "已收藏" : "收藏"}
            >
              <svg
                className="w-4 h-4"
                fill={isSaved ? "currentColor" : "none"}
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </Button>
          </div>
        </div>

        {/* 装饰分割线 */}
        <div className="w-12 h-px bg-gradient-to-r from-primary/50 to-transparent mb-4"></div>

        {/* 中部区域：描述故事 */}
        <div className="mb-4">
          <p className="text-sm text-muted-foreground leading-relaxed font-light">
            "{item.story}"
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

function InspirationGallery({ data }: { data: any }) {
  if (!data) return null;

  return (
    <div className="bg-gradient-to-br from-background to-muted/20 border border-border/30 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
          {data.title}
        </h2>
        <p className="text-muted-foreground lg:text-lg max-w-2xl mx-auto leading-relaxed">
          {data.description}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {data.items?.map((item: any, index: number) => (
          <SeoResultCard key={index} item={item} />
        ))}
      </div>
    </div>
  );
}

function WhyUs({ data }: { data: any }) {
  if (!data) return null;

  return (
    <div className="bg-gradient-to-br from-primary/5 to-secondary/5 border border-primary/20 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="text-center">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-6">
          {data.title}
        </h2>
        <p className="text-muted-foreground lg:text-lg leading-relaxed max-w-3xl mx-auto">
          {data.content}
        </p>
      </div>
    </div>
  );
}

function PromptGuide({ data }: { data: any }) {
  if (!data) return null;

  return (
    <div className="bg-gradient-to-br from-background to-muted/20 border border-border/30 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
          {data.title}
        </h2>
        <p className="text-muted-foreground lg:text-lg max-w-2xl mx-auto leading-relaxed">
          {data.intro}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {data.tips?.map((tip: any, index: number) => (
          <div key={index} className="group p-6 rounded-xl bg-background/50 border border-border/30 hover:bg-background/70 hover:border-border/50 transition-all duration-200 h-full">
            <div className="flex items-start gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-200">
                <span className="text-sm font-bold text-primary">{index + 1}</span>
              </div>
              <h3 className="text-lg font-semibold text-foreground">{tip.title}</h3>
            </div>
            <p className="text-muted-foreground leading-relaxed ml-12">
              {tip.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

function StyleExploration({ data }: { data: any }) {
  if (!data) return null;

  return (
    <div className="bg-gradient-to-br from-background to-muted/20 border border-border/30 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
          {data.title}
        </h2>
        <p className="text-muted-foreground lg:text-lg max-w-2xl mx-auto leading-relaxed">
          {data.intro}
        </p>
      </div>

      <div className="space-y-6">
        {data.items?.map((item: any, index: number) => (
          <div key={index} className="group rounded-xl border border-border/30 overflow-hidden hover:border-border/50 transition-colors duration-200">
            <div className="bg-gradient-to-r from-primary/5 to-secondary/5 px-6 py-4 border-b border-border/30">
              <h3 className="text-xl font-semibold text-foreground">{item.style_title}</h3>
            </div>
            <div className="p-6 bg-background/50">
              <p className="text-muted-foreground leading-relaxed mb-4">
                {item.style_description}
              </p>
              {item.prompt_example && (
                <div className="bg-muted/30 rounded-lg p-4 border-l-4 border-primary/50">
                  <p className="text-sm">
                    <span className="text-muted-foreground">{item.prompt_example}</span>
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function FaqSection({ data }: { data: any }) {
  if (!data) return null;

  return (
    <div className="bg-gradient-to-br from-background to-muted/20 border border-border/30 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
          {data.title}
        </h2>
      </div>

      <div className="max-w-3xl mx-auto">
        {/* 支持新的分类格式 */}
        {data.categories ? (
          <div className="space-y-8">
            {data.categories.map((category: any, categoryIndex: number) => (
              <div key={categoryIndex}>
                <div className="border-b border-border/30 pb-2 mb-4">
                  <h3 className="text-lg font-semibold text-foreground">
                    {category.category}
                  </h3>
                </div>
                <Accordion type="single" collapsible className="w-full space-y-4">
                  {category.items?.map((item: any, itemIndex: number) => (
                    <AccordionItem
                      key={`${categoryIndex}-${itemIndex}`}
                      value={`item-${categoryIndex}-${itemIndex}`}
                      className="border border-border/30 rounded-lg px-6 py-2 hover:bg-background/50 transition-colors duration-200"
                    >
                      <AccordionTrigger className="text-left font-semibold text-foreground hover:no-underline">
                        {item.question}
                      </AccordionTrigger>
                      <AccordionContent className="pb-4">
                        <p className="text-muted-foreground leading-relaxed">
                          {item.answer}
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>
        ) : (
          /* 兼容旧的格式 */
          <Accordion type="single" collapsible className="w-full space-y-4">
            {data.items?.map((item: any, index: number) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className="border border-border/30 rounded-lg px-6 py-2 hover:bg-background/50 transition-colors duration-200"
              >
                <AccordionTrigger className="text-left font-semibold text-foreground hover:no-underline">
                  {item.question}
                </AccordionTrigger>
                <AccordionContent className="pb-4">
                  <p className="text-muted-foreground leading-relaxed">
                    {item.answer}
                  </p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
}

export default function GeneratorSeoContent({ seoContent }: SeoContentProps) {
  if (!seoContent?.seo_content) return null;

  const {
    inspiration_gallery,
    why_us,
    prompt_guide,
    style_exploration,
    faq,
  } = seoContent.seo_content;

  return (
    <div className="space-y-12">
      <InspirationGallery data={inspiration_gallery} />
      <WhyUs data={why_us} />
      <PromptGuide data={prompt_guide} />
      <StyleExploration data={style_exploration} />
      <FaqSection data={faq} />
    </div>
  );
} 