"use client";

import { CoreInteractionZone } from "@/components/core";
import type { CoreInteractionConfig } from "@/types/core-interaction";

export default function HeroInteraction({ config }: { config: CoreInteractionConfig }) {

  return (
    <div className="mt-12 mb-8">
      <CoreInteractionZone
        config={config}
        onGenerate={async (config, prompt, generator, settings) => {
          if (!generator) {
            throw new Error(config.default_message.is_generator_selected);
          }

          if (!prompt.trim()) {
            throw new Error(config.default_message.is_prompt_empty);
          }

          const response = await fetch(`/api/v2/generate-names`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              config: config,
              generator_id: generator.id,
              user_prompt: prompt.trim(),
              settings: {
                name_count: parseInt(settings.nameCount || '5'),
                include_meaning: settings.includeMeaning || false,
                name_length: settings.nameLength || 'medium',
              }
            })
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(config.default_message.api_request_failed_message_status.replace('{status}', response.status.toString()).replace('{message}', errorData.error?.message || ''));
          }

          const data = await response.json();

          if (!data.success) {
            throw new Error(config.default_message.generation_failed);
          }

          return data.data.results;
        }}
      />
    </div>
  );
}
