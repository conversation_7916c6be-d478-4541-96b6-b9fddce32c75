"use client";

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ChevronDown, ChevronUp, Search, Filter } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQCategory {
  category: string;
  items: FAQItem[];
}

interface EnhancedFAQProps {
  faqData: {
    title: string;
    categories: FAQCategory[];
  };
  className?: string;
}

export default function EnhancedFAQ({ faqData, className = "" }: EnhancedFAQProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 切换FAQ项目展开状态
  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // 过滤FAQ项目
  const getFilteredFAQs = () => {
    let filteredCategories = faqData.categories;

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filteredCategories = filteredCategories.filter(cat => cat.category === selectedCategory);
    }

    // 按搜索词过滤
    if (searchTerm) {
      filteredCategories = filteredCategories.map(category => ({
        ...category,
        items: category.items.filter(item =>
          item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.answer.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })).filter(category => category.items.length > 0);
    }

    return filteredCategories;
  };

  const filteredFAQs = getFilteredFAQs();
  const allCategories = ['all', ...faqData.categories.map(cat => cat.category)];

  return (
    <div className={`bg-white dark:bg-slate-900 rounded-2xl p-8 ${className}`}>
      {/* 标题 */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
          {faqData.title}
        </h2>
      </div>

      {/* 搜索和过滤器 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-8">
        {/* 搜索框 */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
          <input
            type="text"
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 placeholder-slate-500 focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        {/* 分类过滤器 */}
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="pl-10 pr-8 py-3 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-primary focus:border-transparent appearance-none cursor-pointer"
          >
            <option value="all">All Categories</option>
            {faqData.categories.map((category) => (
              <option key={category.category} value={category.category}>
                {category.category}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* FAQ内容 */}
      <div className="space-y-6">
        {filteredFAQs.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-slate-500 dark:text-slate-400">
              No questions found matching your search.
            </p>
          </div>
        ) : (
          filteredFAQs.map((category, categoryIndex) => (
            <div key={category.category} className="space-y-4">
              {/* 分类标题 */}
              {selectedCategory === 'all' && (
                <div className="border-b border-slate-200 dark:border-slate-700 pb-2 mb-4">
                  <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200">
                    {category.category}
                  </h3>
                </div>
              )}

              {/* FAQ项目 */}
              {category.items.map((item, itemIndex) => {
                const itemId = `${category.category}-${itemIndex}`;
                const isExpanded = expandedItems.has(itemId);

                return (
                  <div
                    key={itemId}
                    className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden"
                  >
                    {/* 问题 */}
                    <button
                      onClick={() => toggleItem(itemId)}
                      className="w-full px-6 py-4 text-left bg-slate-50 dark:bg-slate-800 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200 flex items-center justify-between"
                    >
                      <h4 className="text-lg font-medium text-slate-900 dark:text-slate-100 pr-4">
                        {item.question}
                      </h4>
                      <div className="flex-shrink-0">
                        {isExpanded ? (
                          <ChevronUp className="w-5 h-5 text-slate-500" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-slate-500" />
                        )}
                      </div>
                    </button>

                    {/* 答案 */}
                    <div
                      className={`
                        transition-all duration-300 ease-in-out overflow-hidden
                        ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
                      `}
                    >
                      <div className="px-6 py-4 bg-white dark:bg-slate-900">
                        <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                          {item.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ))
        )}
      </div>

      {/* 底部提示 */}
      <div className="mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
        <p className="text-center text-sm text-slate-500 dark:text-slate-400">
          Can't find what you're looking for? 
          <a 
            href="mailto:<EMAIL>" 
            className="text-primary hover:text-primary/80 ml-1 font-medium"
          >
            Contact our support team
          </a>
        </p>
      </div>
    </div>
  );
}
