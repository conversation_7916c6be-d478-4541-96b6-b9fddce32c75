"use client";

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ChevronRight, User, Settings, Sparkles } from 'lucide-react';

interface HowToUseStep {
  step: number;
  title: string;
  description: string;
  tips: string[];
  icon: React.ReactNode;
}

interface HowToUseGuideProps {
  generatorType: string;
  howToUseData: any;
  className?: string;
}

export default function HowToUseGuide({ howToUseData, className = "" }: HowToUseGuideProps) {
  const [activeStep, setActiveStep] = useState<number>(1);

  if (!howToUseData) return null;

  // 根据传入的数据获取步骤数据
  const getSteps = (): HowToUseStep[] => {
    return [
      {
        step: 1,
        title: howToUseData.step1.title,
        description: howToUseData.step1.description,
        tips: [
          howToUseData.step1.tip1,
          howToUseData.step1.tip2,
          howToUseData.step1.tip3
        ],
        icon: <User className="w-6 h-6" />
      },
      {
        step: 2,
        title: howToUseData.step2.title,
        description: howToUseData.step2.description,
        tips: [
          howToUseData.step2.tip1,
          howToUseData.step2.tip2,
          howToUseData.step2.tip3
        ],
        icon: <Settings className="w-6 h-6" />
      },
      {
        step: 3,
        title: howToUseData.step3.title,
        description: howToUseData.step3.description,
        tips: [
          howToUseData.step3.tip1,
          howToUseData.step3.tip2,
          howToUseData.step3.tip3
        ],
        icon: <Sparkles className="w-6 h-6" />
      }
    ];
  };

  const steps = getSteps();

  return (
    <div className={`bg-gradient-to-br from-background to-muted/20 border border-border/30 rounded-2xl p-8 lg:p-12 shadow-sm hover:shadow-md transition-shadow duration-300 ${className}`}>
      {/* 标题区域 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold lg:text-3xl bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent mb-4">
          {howToUseData.title}
        </h2>
        <p className="text-muted-foreground lg:text-lg max-w-2xl mx-auto leading-relaxed">
          {howToUseData.intro}
        </p>
      </div>

      {/* 步骤导航 */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-4">
          {steps.map((step, index) => (
            <div key={step.step} className="flex items-center">
              <button
                onClick={() => setActiveStep(step.step)}
                className={`
                  flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300
                  ${activeStep === step.step
                    ? 'bg-primary border-primary text-white shadow-lg scale-110'
                    : 'bg-background border-border text-muted-foreground hover:border-primary hover:text-primary'
                  }
                `}
              >
                <span className="font-semibold">{step.step}</span>
              </button>
              {index < steps.length - 1 && (
                <ChevronRight className="w-5 h-5 text-slate-400 mx-2" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 步骤内容 */}
      <div className="max-w-4xl mx-auto">
        {steps.map((step) => (
          <div
            key={step.step}
            className={`
              transition-all duration-500 ease-in-out
              ${activeStep === step.step ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 absolute'}
            `}
            style={{ display: activeStep === step.step ? 'block' : 'none' }}
          >
            <div className="bg-background/50 rounded-xl p-6 shadow-sm border border-border/50">
              {/* 步骤标题 */}
              <div className="flex items-center mb-4">
                <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mr-4">
                  <div className="text-primary">
                    {step.icon}
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-foreground">
                    {step.title}
                  </h3>
                  <p className="text-muted-foreground mt-1">
                    {step.description}
                  </p>
                </div>
              </div>

              {/* 使用技巧 */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-muted-foreground mb-3 uppercase tracking-wide">
                  {howToUseData.tips_title}
                </h4>
                <div className="grid gap-3">
                  {step.tips.map((tip, tipIndex) => (
                    <div
                      key={tipIndex}
                      className="flex items-start space-x-3 p-3 bg-muted/30 rounded-lg"
                    >
                      <div className="flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2"></div>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {tip}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 底部导航 */}
      <div className="flex justify-between items-center mt-8">
        <button
          onClick={() => setActiveStep(Math.max(1, activeStep - 1))}
          disabled={activeStep === 1}
          className="flex items-center px-4 py-2 text-sm font-medium text-muted-foreground hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <ChevronRight className="w-4 h-4 mr-1 rotate-180" />
          {howToUseData.previous}
        </button>

        <div className="text-sm text-muted-foreground">
          {activeStep} / {steps.length}
        </div>

        <button
          onClick={() => setActiveStep(Math.min(steps.length, activeStep + 1))}
          disabled={activeStep === steps.length}
          className="flex items-center px-4 py-2 text-sm font-medium text-muted-foreground hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {howToUseData.next}
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
}
