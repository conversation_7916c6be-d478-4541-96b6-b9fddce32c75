'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { CommonSettingsProps, CommonSetting } from '@/types/core-interaction';

// 图标组件映射
const IconMap = {
  'hash': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
    </svg>
  ),
  'check': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  ),
  'ruler': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
    </svg>
  ),
  'book-open': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  ),
  'toggle': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
    </svg>
  )
};

// 获取图标组件
const getIcon = (iconName: string) => {
  return IconMap[iconName as keyof typeof IconMap] || IconMap['toggle'];
};

// 下拉选择设置组件
function DropdownSetting({ 
  setting, 
  value, 
  onChange 
}: { 
  setting: CommonSetting; 
  value: any; 
  onChange: (value: any) => void; 
}) {
  const currentOption = setting.options?.find(opt => opt.value === value);
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            // 移动端更紧凑的高度
            "h-9 sm:h-8 px-3 text-sm",
            // 移动端：全宽显示，桌面端：自适应宽度
            "w-full sm:w-auto justify-between sm:justify-center"
          )}
        >
          <div className="flex items-center gap-2">
            {getIcon(setting.icon)}
            <span className="text-sm font-medium">
              {currentOption?.label || setting.label}
            </span>
          </div>
          <svg className="w-3 h-3 opacity-50 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-40">
        {setting.options?.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => onChange(option.value)}
            className={cn(
              "flex items-center gap-2 cursor-pointer",
              value === option.value && "bg-primary/10 text-primary"
            )}
          >
            {option.icon && getIcon(option.icon)}
            <span>{option.label}</span>
            {value === option.value && (
              <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 复选框设置组件
function CheckboxSetting({
  setting,
  value,
  onChange
}: {
  setting: CommonSetting;
  value: boolean;
  onChange: (value: boolean) => void;
}) {
  return (
    <div className={cn(
      "flex items-center space-x-2 p-2 rounded-lg border border-transparent",
      // 移动端：全宽显示，桌面端：自适应宽度
      "w-full sm:w-auto",
      // 悬停效果
      "hover:border-border/30 hover:bg-muted/20 transition-colors duration-200"
    )}>
      <Checkbox
        id={setting.id}
        checked={value}
        onCheckedChange={onChange}
        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
      />
      {setting.description ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <label
                htmlFor={setting.id}
                className="flex items-center gap-2 text-sm font-medium cursor-pointer flex-1"
              >
                {getIcon(setting.icon)}
                <span className="truncate">{setting.label}</span>
              </label>
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-64">{setting.description}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <label
          htmlFor={setting.id}
          className="flex items-center gap-2 text-sm font-medium cursor-pointer flex-1"
        >
          {getIcon(setting.icon)}
          <span className="truncate">{setting.label}</span>
        </label>
      )}
    </div>
  );
}

// 切换按钮设置组件
function ToggleSetting({ 
  setting, 
  value, 
  onChange 
}: { 
  setting: CommonSetting; 
  value: boolean; 
  onChange: (value: boolean) => void; 
}) {
  return (
    <Button
      variant={value ? "default" : "outline"}
      size="sm"
      onClick={() => onChange(!value)}
      className={cn(
        // 移动端更紧凑的高度
        "h-9 sm:h-8 px-3 text-sm",
        // 移动端：全宽显示，桌面端：自适应宽度
        "w-full sm:w-auto justify-center"
      )}
    >
      <div className="flex items-center gap-2">
        {getIcon(setting.icon)}
        <span className="text-sm font-medium">{setting.label}</span>
      </div>
    </Button>
  );
}

export default function CommonSettings({
  settings,
  values,
  onChange,
  className
}: CommonSettingsProps) {
  // 按顺序排序设置项
  const sortedSettings = [...settings].sort((a, b) => a.order - b.order);

  return (
    <div className={cn(
      // 基础布局：移动端网格布局，桌面端水平排列
      "gap-2 sm:gap-3",
      // 移动端：2列网格布局，桌面端：水平排列
      "grid grid-cols-2 sm:flex sm:flex-row sm:items-center sm:flex-wrap",
      // 移动端：每项占满宽度，桌面端：自适应宽度
      "w-full sm:w-auto",
      className
    )}>
      {sortedSettings.map((setting) => {
        const value = values[setting.id];

        const settingComponent = (() => {
          switch (setting.type) {
            case 'dropdown':
              return (
                <DropdownSetting
                  key={setting.id}
                  setting={setting}
                  value={value}
                  onChange={(newValue) => onChange(setting.id, newValue)}
                />
              );

            case 'checkbox':
              return (
                <CheckboxSetting
                  key={setting.id}
                  setting={setting}
                  value={value}
                  onChange={(newValue) => onChange(setting.id, newValue)}
                />
              );

            case 'toggle':
              return (
                <ToggleSetting
                  key={setting.id}
                  setting={setting}
                  value={value}
                  onChange={(newValue) => onChange(setting.id, newValue)}
                />
              );

            default:
              return null;
          }
        })();

        // 在移动端为每个设置项添加全宽样式
        return (
          <div key={setting.id} className="w-full sm:w-auto">
            {settingComponent}
          </div>
        );
      })}
    </div>
  );
}
