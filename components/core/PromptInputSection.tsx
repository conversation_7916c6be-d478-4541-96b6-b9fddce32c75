'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import type { PromptInputSectionProps } from '@/types/core-interaction';

export default function PromptInputSection({
  value,
  onChange,
  placeholder,
  maxLength,
  hint,
  disabled = false,
  className,
  settingsBar
}: PromptInputSectionProps) {
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整文本框高度
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 280)}px`;
    }
  };

  // 当内容变化时调整高度
  useEffect(() => {
    adjustHeight();
  }, [value]);

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxLength) {
      onChange(newValue);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Ctrl/Cmd + Enter 可以触发生成（如果需要的话）
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      // 这里可以触发生成事件，但目前由父组件处理
    }
  };

  const characterCount = value.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isAtLimit = characterCount >= maxLength;

  return (
    <div className={cn("w-full space-y-2", className)}>
      {/* 主输入区域 - 重新设计 */}
      <div className={cn(
        "group relative rounded-xl border-2 bg-background/70 backdrop-blur-sm transition-all duration-300",
        isFocused
          ? "border-primary/60 shadow-xl shadow-primary/15 bg-background/90"
          : "border-border hover:border-primary/50 hover:shadow-lg hover:shadow-primary/8 hover:bg-background/80",
        disabled && "opacity-50 cursor-not-allowed"
      )}>
        {/* 顶部装饰线 */}
        <div className={cn(
          "absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-transparent via-primary/30 to-transparent rounded-b-full transition-all duration-300",
          isFocused ? "opacity-100 w-20" : "opacity-0"
        )} />

        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          rows={3}
          className={cn(
            "w-full resize-none border-0 bg-transparent px-6 py-5 text-base placeholder:text-muted-foreground/60 focus:outline-none focus:ring-0",
            // 移动端确保足够的输入空间，桌面端可以更高
            "min-h-[120px] sm:min-h-[160px] max-h-[280px] leading-relaxed",
            // 移除内嵌设置栏的底部内边距，让输入框有更多空间
            "pb-5"
          )}
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: 'hsl(var(--muted-foreground)) transparent'
          }}
        />

        {/* 字符计数器 - 始终显示在右下角 */}
        <div className="absolute right-5 bottom-4">
          <div className={cn(
            "flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium transition-all duration-200 backdrop-blur-sm",
            isAtLimit
              ? "bg-destructive/10 text-destructive border border-destructive/20"
              : isNearLimit
                ? "bg-orange-100 text-orange-600 border border-orange-200"
                : "bg-background/60 text-muted-foreground border border-border/30"
          )}>
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            {characterCount}/{maxLength}
          </div>
        </div>
      </div>


    </div>
  );
}
