'use client';

import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import CommonSettings from './CommonSettings';
import type { SettingsBarProps } from '@/types/core-interaction';

export default function SettingsBar({
  generatorSelector,
  commonSettings,
  settingsValues,
  onSettingChange,
  className
}: Omit<SettingsBarProps, 'actions'>) {
  return (
    <div className={cn(
      // 基础布局 - 移动端更紧凑的间距
      "flex gap-3 px-4 py-3",
      // 移动端：垂直排列，桌面端：水平排列
      "flex-col sm:flex-row sm:items-center",
      className
    )}>
      {/* 生成器选择器 - 桌面端限制宽度，移动端全宽 */}
      <div className={cn(
        "flex-shrink-0",
        // 桌面端：限制最大宽度，避免挤压设置区域
        "w-full sm:w-auto sm:max-w-[240px]"
      )}>
        {generatorSelector}
      </div>

      {/* 分隔线 - 只在桌面端显示 */}
      {commonSettings.length > 0 && (
        <Separator orientation="vertical" className="h-5 hidden sm:block opacity-50" />
      )}

      {/* 通用设置 */}
      {commonSettings.length > 0 && (
        <div className={cn(
          "flex-1 min-w-0",
          // 移动端：全宽显示，桌面端：自适应剩余空间
          "w-full sm:w-auto"
        )}>
          <CommonSettings
            settings={commonSettings}
            values={settingsValues}
            onChange={onSettingChange}
            className="justify-start"
          />
        </div>
      )}
    </div>
  );
}
