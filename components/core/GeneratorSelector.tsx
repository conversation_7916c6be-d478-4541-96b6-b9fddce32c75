'use client';

import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { GeneratorSelectorProps } from '@/types/core-interaction';

// 图标组件映射
const IconMap = {
  'magic-wand': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
  ),
  'sparkles': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
    </svg>
  ),
  'user': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  ),
  'crown': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l2 9h10l2-9M5 3l-2 9h4M19 3l2 9h-4M12 3v9" />
    </svg>
  ),
  'sword': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2l3.09 6.26L22 9l-5 4.74L18.18 22 12 18.27 5.82 22 7 13.74 2 9l6.91-.74L12 2z" />
    </svg>
  ),
  'rocket': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    </svg>
  ),
  'building': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  ),
  'fire': (
    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
    </svg>
  )
};

// 获取图标组件
const getIcon = (iconName: string) => {
  return IconMap[iconName as keyof typeof IconMap] || IconMap['magic-wand'];
};

// 文本省略函数
const truncateText = (text: string, maxLength: number = 20) => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - 3) + '...';
};

export default function GeneratorSelector({
  config,
  selectedGenerator,
  onClick,
  className
}: GeneratorSelectorProps) {
  const isSelected = !!selectedGenerator;

  // 根据状态确定显示内容
  const displayIcon = isSelected
    ? getIcon(selectedGenerator.icon)
    : getIcon(config.defaultIcon);

  const fullText = isSelected
    ? selectedGenerator.name
    : config.defaultText;

  // 省略长文本
  const displayText = truncateText(fullText);
  const shouldShowTooltip = fullText.length > 20;

  const buttonContent = (
    <Button
      variant={isSelected ? "default" : "outline"}
      onClick={onClick}
      className={cn(
        "h-10 px-4 transition-all duration-200 hover:scale-105",
        // 确保按钮不会过宽
        "max-w-full",
        isSelected
          ? "bg-primary text-primary-foreground shadow-md"
          : "border-dashed border-2 hover:border-primary/50 hover:bg-primary/5",
        className
      )}
    >
      <div className="flex items-center gap-2 min-w-0">
        {/* 图标 */}
        <div className={cn(
          "transition-transform duration-200 flex-shrink-0",
          isSelected ? "scale-110" : "scale-100"
        )}>
          {displayIcon}
        </div>

        {/* 文本 - 允许省略 */}
        <span className={cn(
          "font-medium text-sm truncate",
          // 在小屏幕上进一步限制宽度
          "max-w-[120px] sm:max-w-[160px]"
        )}>
          {displayText}
        </span>

        {/* 下拉箭头 */}
        <svg
          className={cn(
            "w-3 h-3 transition-transform duration-200 ml-1 flex-shrink-0",
            "opacity-60"
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      {/* 选中状态指示器 */}
      {isSelected && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
      )}
    </Button>
  );

  // 如果需要显示完整文本，使用Tooltip包装
  if (shouldShowTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {buttonContent}
          </TooltipTrigger>
          <TooltipContent>
            <p>{fullText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return buttonContent;
}
