'use client';

import { useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import type {
  CoreInteractionZoneProps,
  CoreInteractionState,
  GeneratorCard
} from '@/types/core-interaction';

import PromptInputSection from './PromptInputSection';
import SettingsBar from './SettingsBar';
import GeneratorSelector from './GeneratorSelector';
import GeneratorModal from './GeneratorModal';
import ResultsSection from './ResultsSection';

export default function CoreInteractionZone({
  config,
  initialState,
  onGenerate,
  onStateChange,
  className
}: CoreInteractionZoneProps) {
  // 初始化状态
  const [state, setState] = useState<CoreInteractionState>(() => {
    const defaultSettings = config.commonSettings.reduce((acc, setting) => {
      acc[setting.id] = setting.defaultValue;
      return acc;
    }, {} as Record<string, any>);

    return {
      userPrompt: '',
      selectedGenerator: null,
      settings: defaultSettings,
      isGeneratorModalOpen: false,
      isGenerating: false,
      results: [],
      resultsVisible: false,
      error: undefined,
      ...initialState
    };
  });

  // 更新状态的通用方法
  const updateState = useCallback((updates: Partial<CoreInteractionState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      onStateChange?.(newState);
      return newState;
    });
  }, [onStateChange]);

  // 处理提示词输入变化
  const handlePromptChange = useCallback((value: string) => {
    updateState({ userPrompt: value });
  }, [updateState]);

  // 处理生成器选择
  const handleGeneratorSelect = useCallback((generator: GeneratorCard) => {
    updateState({
      selectedGenerator: generator,
      isGeneratorModalOpen: false
    });
  }, [updateState]);

  // 处理设置变化
  const handleSettingChange = useCallback((settingId: string, value: any) => {
    updateState({
      settings: {
        ...state.settings,
        [settingId]: value
      }
    });
  }, [state.settings, updateState]);

  // 处理清空
  const handleClear = useCallback(() => {
    updateState({
      userPrompt: '',
      results: [],
      resultsVisible: false,
      error: undefined
    });
  }, [updateState]);

  // 模拟生成结果数据
  const generateMockResults = (prompt: string, generator: any, settings: any) => {
    const nameCount = parseInt(settings.nameCount || '5');
    const includeMeaning = settings.includeMeaning || false;

    // 根据生成器类型生成不同的结果
    const generatorType = generator?.id || 'character-name-generator';

    const mockResults = [];
    for (let i = 0; i < nameCount; i++) {
      let result;

      switch (generatorType) {
        case 'elf-name-generator':
          result = {
            id: `elf-${i}`,
            name: ['Aelindra', 'Thalorin', 'Silviana', 'Erevan', 'Lyralei', 'Caelynn', 'Valdris', 'Nimrodel'][i] || `精灵名字${i + 1}`,
            pronunciation: ['艾琳德拉', '萨洛林', '西尔维安娜', '埃雷万', '莱拉蕾', '凯琳', '瓦德里斯', '尼姆罗德尔'][i],
            description: includeMeaning ?
              ['月光下的守护者，拥有纯净的心灵和敏锐的直觉', '森林的智者，精通古老的魔法和自然之道', '银色月光的化身，带来希望与治愈', '星辰的使者，指引迷失的灵魂', '风中的歌者，用音乐治愈创伤', '晨曦的女儿，象征新的开始', '暗影中的守护者，保护族人免受邪恶', '永恒之树的守护者，维护自然平衡'][i] :
              ['一个优雅的精灵名字，适合高贵的角色', '充满智慧的精灵名字，适合法师或学者', '美丽的精灵名字，带有神秘色彩', '古老的精灵名字，充满传奇色彩', '音乐天赋的精灵名字', '光明系精灵的经典名字', '适合游侠或刺客的精灵名字', '自然系精灵的传统名字'][i],
            tags: ['精灵', '奇幻', '高雅', '神秘']
          };
          break;

        case 'dragon-name-generator':
          result = {
            id: `dragon-${i}`,
            name: ['Pyrothane', 'Azurewing', 'Shadowmaw', 'Crystalclaw', 'Stormheart'][i] || `龙族名字${i + 1}`,
            pronunciation: ['派罗森', '阿祖尔翼', '暗影之颚', '水晶爪', '风暴之心'][i],
            description: includeMeaning ?
              ['烈火之王，掌控着毁灭与重生的力量', '天空的统治者，翱翔于云端之上', '黑暗的主宰，潜伏在阴影深处', '大地的守护者，拥有坚不可摧的鳞甲', '雷电的化身，呼风唤雨的古老存在'][i] :
              ['威严的火龙名字，适合强大的角色', '优雅的蓝龙名字，象征智慧', '神秘的暗龙名字，充满威胁感', '坚韧的土龙名字，代表力量', '狂野的雷龙名字，象征自由'][i],
            tags: ['龙族', '威严', '古老', '强大']
          };
          break;

        default:
          result = {
            id: `character-${i}`,
            name: ['Aria', 'Kael', 'Luna', 'Zara', 'Orion'][i] || `角色名字${i + 1}`,
            pronunciation: ['阿里亚', '凯尔', '露娜', '扎拉', '奥里昂'][i],
            description: includeMeaning ?
              ['勇敢的战士，拥有不屈的意志', '智慧的法师，精通古老的魔法', '月亮的女儿，带来宁静与和平', '沙漠的玫瑰，美丽而坚强', '星空的猎人，追寻着命运的指引'][i] :
              ['适合勇敢角色的经典名字', '充满智慧的角色名字', '优美的女性角色名字', '异域风情的角色名字', '英雄气质的男性名字'][i],
            tags: ['通用', '经典', '适用性强']
          };
      }

      mockResults.push(result);
    }

    return mockResults;
  };

  // 处理生成
  const handleGenerate = useCallback(async () => {
    if (!state.userPrompt.trim()) return;

    updateState({ isGenerating: true, error: undefined, resultsVisible: true });

    try {
      // 如果有外部的 onGenerate 函数，使用它
      if (onGenerate) {
        const results = await onGenerate(config, state.userPrompt, state.selectedGenerator, state.settings);
        updateState({ results: results });
      } else {
        // 否则使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟网络延迟
        const mockResults = generateMockResults(state.userPrompt, state.selectedGenerator, state.settings);
        updateState({ results: mockResults });
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : '生成失败，请重试'
      });
    } finally {
      updateState({ isGenerating: false });
    }
  }, [state.userPrompt, state.selectedGenerator, state.settings, onGenerate, updateState, config]);

  // 获取当前占位符文本
  const getCurrentPlaceholder = () => {
    return state.selectedGenerator?.placeholder || config.promptInput.defaultPlaceholder;
  };

  // 生成器选择器组件
  const generatorSelector = (
    <GeneratorSelector
      config={config.generatorSelector}
      selectedGenerator={state.selectedGenerator}
      onClick={() => updateState({ isGeneratorModalOpen: true })}
    />
  );

  // 操作按钮组件 - 重新设计
  const actions = (
    <div className="flex gap-3">
      <button
        type="button"
        onClick={handleClear}
        disabled={state.isGenerating}
        className={cn(
          "group px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 disabled:opacity-50",
          "border border-border/30 text-muted-foreground hover:text-foreground",
          "hover:bg-muted/30 hover:border-border/50 hover:shadow-sm",
          "active:scale-95"
        )}
      >
        <span className="flex items-center gap-2">
          <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          {config.actions.clearText}
        </span>
      </button>

      <button
        type="button"
        onClick={handleGenerate}
        disabled={!state.userPrompt.trim() || state.isGenerating}
        className={cn(
          "group relative px-6 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",
          "bg-gradient-to-r from-primary to-primary/90 text-primary-foreground",
          "hover:from-primary/90 hover:to-primary hover:shadow-lg hover:shadow-primary/25",
          "active:scale-95",
          state.isGenerating && "animate-pulse"
        )}
      >
        <span className="flex items-center gap-2">
          {state.isGenerating ? (
            <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="w-4 h-4 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          )}
          {state.isGenerating ? config.actions.generatingText : config.actions.generateText}
        </span>

        {/* 生成按钮的发光效果 */}
        {!state.isGenerating && state.userPrompt.trim() && (
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10 blur-sm" />
        )}
      </button>
    </div>
  );

  return (
    <div className={cn("w-full max-w-4xl mx-auto space-y-4", className)}>
      {/* 主输入框和设置栏分离布局 */}
      <div className="space-y-3">
        {/* 主输入框（独立，不内嵌设置栏） */}
        <PromptInputSection
          value={state.userPrompt}
          onChange={handlePromptChange}
          placeholder={getCurrentPlaceholder()}
          maxLength={config.promptInput.maxLength}
          disabled={state.isGenerating}
        />

        {/* 设置栏（独立区域） */}
        <div className={cn(
          "rounded-xl border-2 border-border/60 bg-background/70 backdrop-blur-sm",
          "hover:border-primary/30 hover:bg-background/80 transition-all duration-200"
        )}>
          <SettingsBar
            generatorSelector={generatorSelector}
            commonSettings={config.commonSettings}
            settingsValues={state.settings}
            onSettingChange={handleSettingChange}
          />
        </div>

        {/* 底部操作区域 - 重新设计 */}
        <div className="flex items-center justify-between px-1 mt-4">
          {/* 左侧：快捷键提示（仅在有内容时显示） */}
          <div className="flex-1">
            {state.userPrompt.trim() && (
              <div className="hidden sm:flex items-center gap-2 text-xs text-muted-foreground/70">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-0.5 bg-muted/50 border border-border/30 rounded text-xs font-medium">Ctrl</kbd>
                  <span>+</span>
                  <kbd className="px-2 py-0.5 bg-muted/50 border border-border/30 rounded text-xs font-medium">Enter</kbd>
                  <span className="ml-1 font-light">{config.actions.generateTextHint}</span>
                </span>
              </div>
            )}
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex-shrink-0">
            {actions}
          </div>
        </div>
      </div>

      {/* 错误提示 */}
      {state.error && (
        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <p className="text-sm text-destructive">{state.error}</p>
        </div>
      )}

      {/* 结果展示区 */}
      <ResultsSection
        results={state.results}
        loading={state.isGenerating}
        visible={state.resultsVisible}
        onRegenerate={handleGenerate}
      />

      {/* 生成器选择模态框 */}
      <GeneratorModal
        isOpen={state.isGeneratorModalOpen}
        onClose={() => updateState({ isGeneratorModalOpen: false })}
        title={config.generatorSelector.modalTitle}
        categories={config.categories}
        generators={config.generators}
        selectedGenerator={state.selectedGenerator}
        onSelect={handleGeneratorSelect}
      />
    </div>
  );
}
