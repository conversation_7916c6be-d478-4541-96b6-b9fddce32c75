'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { Skeleton } from '@/components/ui/skeleton';
import type { ResultsSectionProps } from '@/types/core-interaction';
import { useTranslations } from 'next-intl';

// 模拟的结果数据类型（临时使用，后续会使用真实的 GeneratedName 类型）
interface MockResult {
  id: string;
  name: string;
  description: string;
  pronunciation?: string;
  tags?: string[];
}

// 结果卡片组件
function ResultCard({
  result,
  onSave
}: {
  result: MockResult;
  onSave?: (result: MockResult) => void;
}) {
  const [isSaved, setIsSaved] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleSave = () => {
    setIsSaved(true);
    onSave?.(result);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result.name);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  return (
    <Card
      className={cn(
        "group relative transition-all duration-300 cursor-pointer",
        "border border-border/50 bg-card/50 backdrop-blur-sm",
        "hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1",
        "hover:border-primary/30 hover:bg-card/80"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-6">
        {/* 顶部区域：名字 + 操作按钮 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            {/* 主名字 */}
            <h3 className="text-2xl font-bold text-foreground mb-1 tracking-wide">
              {result.name}
            </h3>

            {/* 发音 - 弱化处理 */}
            {result.pronunciation && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground/70">
                <svg className="w-3.5 h-3.5 opacity-60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M8.586 8.586A2 2 0 0011.414 5.414L15 9l-3.586 3.586A2 2 0 008.586 8.586z" />
                </svg>
                <span className="italic font-light">{result.pronunciation}</span>
              </div>
            )}
          </div>

          {/* 操作按钮 - 始终显示但透明度变化 */}
          <div className={cn(
            "flex gap-2 transition-opacity duration-200",
            isHovered ? "opacity-100" : "opacity-30"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className={cn(
                "h-8 w-8 p-0 rounded-full transition-all duration-200",
                "hover:bg-primary/10 hover:text-primary",
                copySuccess && "bg-green-100 text-green-600"
              )}
              title="复制名字"
            >
              {copySuccess ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              disabled={isSaved}
              className={cn(
                "h-8 w-8 p-0 rounded-full transition-all duration-200",
                isSaved
                  ? "bg-red-100 text-red-500 hover:bg-red-100 hover:text-red-500"
                  : "hover:bg-primary/10 hover:text-primary"
              )}
              title={isSaved ? "已收藏" : "收藏"}
            >
              <svg
                className="w-4 h-4"
                fill={isSaved ? "currentColor" : "none"}
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </Button>
          </div>
        </div>

        {/* 装饰分割线 */}
        <div className="w-12 h-px bg-gradient-to-r from-primary/50 to-transparent mb-4"></div>

        {/* 中部区域：描述故事 */}
        <div className="mb-4">
          <p className="text-sm text-muted-foreground leading-relaxed font-light">
            "{result.description}"
          </p>
        </div>

        {/* 底部区域：标签 */}
        {result.tags && result.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {result.tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-muted/50 text-muted-foreground border border-border/30 transition-colors hover:bg-muted/80"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* 悬停时的发光效果 */}
        <div className={cn(
          "absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-0 transition-opacity duration-300 pointer-events-none",
          isHovered && "opacity-100"
        )} />
      </CardContent>
    </Card>
  );
}

// 加载骨架屏
function ResultSkeleton() {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-3">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-16 w-full" />
          <div className="flex gap-2">
            <Skeleton className="h-5 w-12" />
            <Skeleton className="h-5 w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function ResultsSection({
  results,
  loading,
  visible,
  onSave,
  onRegenerate,
  className
}: ResultsSectionProps) {
  // 如果不可见且没有加载，不渲染任何内容
  if (!visible && !loading) {
    return null;
  }

  const t = useTranslations('core.resultsSection');

  return (
    <div className={cn(
      "w-full transition-all duration-500",
      visible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
      className
    )}>
      {/* 标题栏 - 重新设计 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          {/* AI魔法图标 */}
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/5 border border-primary/20">
            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>

          <div>
            <div className="flex items-center gap-3 mb-1">
              <h2 className="text-2xl font-bold text-foreground">{t('ai_creation_results')}</h2>
              {loading && (
                <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 border border-primary/20">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-primary">{t('crafting')}</span>
                </div>
              )}
            </div>

            <p className="text-sm text-muted-foreground flex items-center gap-2">
              {loading ? (
                <>
                  <svg className="w-4 h-4 animate-spin text-primary" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('ai_crafting_names')}
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  {t('ai_created_names', { count: results.length })}
                </>
              )}
            </p>
          </div>
        </div>

        {!loading && results.length > 0 && onRegenerate && (
          <Button
            variant="outline"
            onClick={onRegenerate}
            className="flex items-center gap-2 px-4 py-2 border-primary/30 text-primary hover:bg-primary/5 hover:border-primary/50 transition-all duration-200"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {t('regenerate')}
          </Button>
        )}
      </div>

      {/* 结果网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {loading ? (
          // 加载状态
          Array.from({ length: 4 }).map((_, index) => (
            <ResultSkeleton key={index} />
          ))
        ) : (
          // 结果展示
          results.map((result: any, index) => (
            <ResultCard
              key={result.id || index}
              result={result}
              onSave={onSave}
            />
          ))
        )}
      </div>

      {/* 空状态 - 重新设计 */}
      {!loading && results.length === 0 && visible && (
        <div className="text-center py-16">
          <div className="relative mb-6">
            {/* 背景装饰圆圈 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20"></div>
            </div>
            {/* 魔法棒图标 */}
            <div className="relative flex items-center justify-center">
              <svg className="w-12 h-12 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
          </div>

          <h3 className="text-xl font-semibold mb-3 text-foreground">{t('ready_to_create_magic')}</h3>
          <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
            {t('input_your_creative_description')}
          </p>
        </div>
      )}
    </div>
  );
}
