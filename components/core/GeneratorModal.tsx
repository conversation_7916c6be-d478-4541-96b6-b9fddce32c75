'use client';

import { useState, useMemo } from 'react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { GeneratorModalProps, GeneratorCard } from '@/types/core-interaction';

// 图标组件映射（复用 GeneratorSelector 的图标）
const IconMap = {
  'magic-wand': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
    </svg>
  ),
  'sparkles': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
    </svg>
  ),
  'user': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
  ),
  'crown': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l2 9h10l2-9M5 3l-2 9h4M19 3l2 9h-4M12 3v9" />
    </svg>
  ),
  'sword': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2l3.09 6.26L22 9l-5 4.74L18.18 22 12 18.27 5.82 22 7 13.74 2 9l6.91-.74L12 2z" />
    </svg>
  ),
  'rocket': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    </svg>
  ),
  'building': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  ),
  'fire': (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
    </svg>
  )
};

// 获取图标组件
const getIcon = (iconName: string) => {
  return IconMap[iconName as keyof typeof IconMap] || IconMap['magic-wand'];
};

// 生成器卡片组件
function GeneratorCardComponent({ 
  generator, 
  isSelected, 
  onClick 
}: { 
  generator: GeneratorCard; 
  isSelected: boolean; 
  onClick: () => void; 
}) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full p-4 rounded-lg border-2 transition-all duration-200 text-left",
        "hover:shadow-md hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50",
        isSelected 
          ? "border-primary bg-primary/5 shadow-md" 
          : "border-border hover:border-primary/50 hover:bg-muted/50"
      )}
    >
      <div className="flex items-start gap-3">
        {/* 图标 */}
        <div className={cn(
          "flex-shrink-0 p-2 rounded-md transition-colors",
          isSelected 
            ? "bg-primary text-primary-foreground" 
            : "bg-muted text-muted-foreground"
        )}>
          {getIcon(generator.icon)}
        </div>
        
        {/* 内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-sm truncate">{generator.name}</h3>
            {generator.badge && (
              <Badge 
                variant={generator.badge === 'hot' ? 'destructive' : 'secondary'}
                className="text-xs px-1.5 py-0.5"
              >
                {generator.badge === 'hot' ? '🔥 Hot' : 
                 generator.badge === 'new' ? '✨ New' : 
                 '⭐ Recommended'}
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground line-clamp-2">
            {generator.description}
          </p>
        </div>
        
        {/* 选中指示器 */}
        {isSelected && (
          <div className="flex-shrink-0">
            <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
        )}
      </div>
    </button>
  );
}

export default function GeneratorModal({
  isOpen,
  onClose,
  title,
  categories,
  generators,
  selectedGenerator,
  onSelect
}: GeneratorModalProps) {
  // 过滤有生成器的分类
  const usedCategories = new Set(generators.filter(g => g.isActive).map(g => g.category));
  const filteredCategories = categories.filter(cat =>
    cat.id === 'all' || usedCategories.has(cat.id)
  );

  const [activeCategory, setActiveCategory] = useState(filteredCategories[0]?.id || 'all');

  // 按分类过滤生成器
  const filteredGenerators = useMemo(() => {
    if (activeCategory === 'all') {
      return generators.filter(g => g.isActive).sort((a, b) => a.order - b.order);
    }
    return generators
      .filter(g => g.isActive && g.category === activeCategory)
      .sort((a, b) => a.order - b.order);
  }, [generators, activeCategory]);

  // 处理生成器选择
  const handleSelect = (generator: GeneratorCard) => {
    onSelect(generator);
    onClose();
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">{title}</DialogTitle>
        </DialogHeader>
        
        <div className="flex flex-col h-full">
          {/* 分类标签 */}
          <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-6">
              {filteredCategories.map((category) => (
                <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                  {getIcon(category.icon)}
                  <span>{category.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>
            
            {/* 生成器网格 */}
            <div className="flex-1 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">

                
                {/* 其他生成器 */}
                {filteredGenerators.map((generator) => (
                  <GeneratorCardComponent
                    key={generator.id}
                    generator={generator}
                    isSelected={selectedGenerator?.id === generator.id}
                    onClick={() => handleSelect(generator)}
                  />
                ))}
              </div>
              
              {/* 空状态 */}
              {filteredGenerators.length === 0 && activeCategory !== 'all' && (
                <div className="text-center py-12">
                  <div className="text-muted-foreground mb-2">
                    <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                  </div>
                  <p className="text-sm text-muted-foreground">该分类下暂无可用的生成器</p>
                </div>
              )}
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
