// 核心交互组件导出
export { default as CoreInteractionZone } from './CoreInteractionZone';
export { default as PromptInputSection } from './PromptInputSection';
export { default as SettingsBar } from './SettingsBar';
export { default as GeneratorSelector } from './GeneratorSelector';
export { default as GeneratorModal } from './GeneratorModal';
export { default as CommonSettings } from './CommonSettings';
export { default as ResultsSection } from './ResultsSection';

// 类型导出
export type {
  CoreInteractionZoneProps,
  CoreInteractionConfig,
  CoreInteractionState,
  GeneratorCard,
  GeneratorCategory,
  CommonSetting,
  PromptInputSectionProps,
  SettingsBarProps,
  GeneratorSelectorProps,
  GeneratorModalProps,
  CommonSettingsProps,
  ResultsSectionProps
} from '@/types/core-interaction';
