'use client';

import { cn } from '@/lib/utils';
import type { GeneratorCard as GeneratorCardType } from '@/types/core-interaction';

interface GeneratorCardProps {
  generator: GeneratorCardType;
  onClick: (generator: GeneratorCardType) => void;
  className?: string;
}

export default function GeneratorCard({
  generator,
  onClick,
  className
}: GeneratorCardProps) {
  const handleClick = () => {
    onClick(generator);
  };

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case 'hot':
        return 'bg-red-500 text-white';
      case 'new':
        return 'bg-green-500 text-white';
      case 'popular':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getBadgeText = (badge: string) => {
    switch (badge) {
      case 'hot':
        return 'Hot';
      case 'new':
        return 'New';
      case 'popular':
        return 'Recommended';
      default:
        return badge;
    }
  };

  return (
    <div
      onClick={handleClick}
      className={cn(
        "relative p-4 border rounded-xl cursor-pointer transition-all duration-200",
        "hover:border-primary hover:shadow-lg hover:shadow-primary/10",
        "group bg-background",
        className
      )}
    >
      {/* 角标 */}
      {generator.badge && (
        <div className={cn(
          "absolute -top-2 -right-2 px-2 py-1 rounded-full text-xs font-medium",
          getBadgeColor(generator.badge)
        )}>
          {getBadgeText(generator.badge)}
        </div>
      )}

      {/* 图标 */}
      <div className="flex items-center justify-center w-12 h-12 mb-3 mx-auto">
        <span className="text-2xl">{generator.icon}</span>
      </div>

      {/* 名称 */}
      <h3 className="text-sm font-medium text-center mb-2 group-hover:text-primary transition-colors">
        {generator.name}
      </h3>

      {/* 描述 */}
      {generator.description && (
        <p className="text-xs text-muted-foreground text-center leading-relaxed">
          {generator.description}
        </p>
      )}
    </div>
  );
}
