export interface SeoContentItem {
  name: string;
  story: string;
  pronunciation?: string;
}

export interface SeoContentTip {
  title: string;
  description: string;
}

export interface SeoContentStyleItem {
  style_title: string;
  style_description: string;
  prompt_example?: string;
}

export interface SeoContentFaqItem {
  question: string;
  answer: string;
}

export interface SeoContentRelatedItem {
  title: string;
  description: string;
  url: string;
  icon: string;
}

export interface SeoContentInspirationGallery {
  title: string;
  description: string;
  items: SeoContentItem[];
}

export interface SeoContentWhyUs {
  title: string;
  content: string;
}

export interface SeoContentPromptGuide {
  title: string;
  intro: string;
  tips: SeoContentTip[];
}

export interface SeoContentStyleExploration {
  title: string;
  intro: string;
  items: SeoContentStyleItem[];
}

export interface SeoContentFaq {
  title: string;
  items?: SeoContentFaqItem[];
  categories?: {
    category: string;
    items: SeoContentFaqItem[];
  }[];
  related_generators?: {
    title: string;
    items: SeoContentRelatedItem[];
  };
}

export interface SeoContentHowToUseStep {
  title: string;
  description: string;
  tip1: string;
  tip2: string;
  tip3: string;
}

export interface SeoContentHowToUse {
  title: string;
  intro: string;
  step1: SeoContentHowToUseStep;
  step2: SeoContentHowToUseStep;
  step3: SeoContentHowToUseStep;
  tips_title: string;
  previous: string;
  next: string;
}

export interface GeneratorSeoContentData {
  show_generator?: boolean;
  seo_content: {
    how_to_use?: SeoContentHowToUse;
    inspiration_gallery: SeoContentInspirationGallery;
    why_us: SeoContentWhyUs;
    prompt_guide: SeoContentPromptGuide;
    style_exploration: SeoContentStyleExploration;
    faq: SeoContentFaq;
  };
}