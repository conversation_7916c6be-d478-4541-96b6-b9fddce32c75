// 核心交互 API 类型 定义
import type { CoreInteractionConfig } from '@/types/core-interaction';

// 请求接口
export interface CoreInteractionRequest {
  config: CoreInteractionConfig;
  generator_id: string;          // 生成器ID (如 "elf", "dwarf", "dragon")
  user_prompt: string;           // 用户输入的描述
  settings: {
    name_count: number;          // 名字数量 (3, 5, 8)
    include_meaning: boolean;    // 是否包含含义
    name_length: 'short' | 'medium' | 'long';  // 名字长度
  };
}

// 响应接口
export interface CoreInteractionResponse {
  success: boolean;
  data?: {
    generation_id: string;       // 生成记录ID
    results: GeneratedResult[];  // 生成结果列表
  };
  error?: {
    code: string;               // 错误代码
    message: string;            // 错误信息
  };
  meta?: {
    duration_ms: number;        // 请求处理时间（毫秒）
    timestamp: string;          // 响应时间戳
    version: string;            // API版本
  };
}

// 生成结果项
export interface GeneratedResult {
  id: string;                   // 唯一标识
  name: string;                 // 名字
  pronunciation?: string;       // 发音
  description: string;          // 描述/含义
  tags: string[];              // 标签数组
}

// 生成服务输入接口
export interface GenerateNamesV2Input {
  user_uuid?: string;
  generator_id: string;
  user_prompt: string;
  settings: {
    name_count: number;
    include_meaning: boolean;
    name_length: 'short' | 'medium' | 'long';
  };
}

// 生成服务输出接口
export interface GenerateNamesV2Output {
  generation_id: string;
  results: GeneratedResult[];
}

// 错误代码枚举
export enum ApiErrorCode {
  INVALID_REQUEST = 'INVALID_REQUEST',
  GENERATOR_NOT_FOUND = 'GENERATOR_NOT_FOUND',
  GENERATION_FAILED = 'GENERATION_FAILED',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}
