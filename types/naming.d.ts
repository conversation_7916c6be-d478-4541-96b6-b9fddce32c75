// 名字生成记录接口
export interface NameGeneration {
  uuid: string;
  user_uuid?: string;
  generator_type: string;
  user_prompt: string;
  parameters: {
    gender?: 'male' | 'female' | 'neutral';
    complexity?: 'simple' | 'standard' | 'elaborate';
    template?: string;
    [key: string]: any; // 允许扩展其他参数
  };
  results: GeneratedName[];
  created_at: string;
}

// AI生成的单个名字接口
export interface GeneratedName {
  name: string;
  description: string;
  pronunciation?: string;
  tags: string[];
}

// 用户保存的名字接口
export interface SavedName {
  uuid: string;
  user_uuid: string;
  name: string;
  description: string;
  generator_type: string;
  source_generation_uuid?: string;
  created_at: string;
  complexity: 'simple' | 'standard' | 'elaborate';
  template: 'high_elf' | 'wood_elf' | 'dark_elf' | 'moon_elf' | 'sun_elf';
  
  // 用户输入
  userPrompt: string;
}

// 生成器配置接口
export interface Generator {
  type: string;
  title: string;
  description: string;
  prompt_template: string;
  parameters: {
    gender?: string[];
    complexity?: string[];
    template?: string[];
    [key: string]: any;
  };
  is_active: boolean;
}

// 生成请求接口
export interface GenerateNamesRequest {
  generator_type: string;
  user_prompt: string;
  parameters?: {
    gender?: string;
    complexity?: string;
    template?: string;
    count?: number;
    [key: string]: any;
  };
}

// 生成响应接口
export interface GenerateNamesResponse {
  success: boolean;
  generation?: NameGeneration;
  results: GeneratedName[];
  error?: string;
}

// 保存名字请求接口
export interface SaveNameRequest {
  name: string;
  description: string;
  generator_type: string;
  source_generation_uuid?: string;
}

// 获取用户名字响应接口
export interface GetUserNamesResponse {
  success: boolean;
  names: SavedName[];
  total: number;
}

// Elf Name Generator 专用接口
export interface ElfNameState {
  gender: 'male' | 'female' | 'neutral';
  complexity: 'simple' | 'standard' | 'elaborate';
  template: 'high_elf' | 'wood_elf' | 'dark_elf' | 'moon_elf' | 'sun_elf';
  
  // 用户输入
  userPrompt: string;
  
  // 生成状态
  loading: boolean;
  results: GeneratedName[];
  
  // 收藏状态
  savedNames: SavedName[];
  savingStates: Record<string, boolean>;
}

export interface ElfNameParameters {
  gender: 'male' | 'female' | 'any';
  complexity: 'simple' | 'standard' | 'elaborate';
  template: 'high_elf' | 'wood_elf' | 'dark_elf' | 'moon_elf' | 'sun_elf';
  count: number;
}

// Elf子类型配置
export interface ElfSubtype {
  key: string;
  name: string;
  description: string;
  icon: string;
  traits: string[];
}

// 提示词模板
export interface PromptTemplate {
  generator_type: string;
  template_key: string;
  system_prompt: string;
  user_prompt_template: string;
  parameters_mapping: Record<string, string>;
  example_output: string;
}

// --- General Purpose Generator Types ---

// The structure of the 'parameters' JSON object in the nomenus_generators table
export interface GeneratorParameters {
  controls: {
    key: string;
    label_t: string;
    type: 'select' | 'select_icon' | 'toggle' | 'input';
    options_t: string;
  }[];
  user_prompt: {
    label_t: string;
    placeholder_t: string;
    hint_t: string;
  };
}

// Corresponds to a single row in the nomenus_generators table
export interface GeneratorConfig {
  id: number;
  type: string;
  title: string;
  description: string;
  prompt_template: string;
  parameters: GeneratorParameters;
  is_active: boolean;
  created_at: string;
}

export interface GeneratorState {
  parameters: { [key: string]: string };
  userPrompt: string;
  loading: boolean;
  results: GeneratedName[];
  savedNames: SavedName[];
  savingStates: Record<string, boolean>;
} 