// 核心交互区域相关类型定义

// 生成器分类
export interface GeneratorCategory {
  id: string;
  name: string;
  icon: string;
  order: number;
}

// 生成器卡片
export interface GeneratorCard {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
  badge?: 'hot' | 'new' | 'popular';
  placeholder: string; // 选中后的占位符文本
  order: number;
  isActive: boolean;
  // 新增：系统 prompt 配置
  systemPrompt?: string;         // 系统 prompt 模板
  promptVariables?: string[];    // prompt 中使用的变量列表
}

// 设置选项
export interface SettingOption {
  value: string;
  label: string;
  icon?: string;
}

// 通用设置项
export interface CommonSetting {
  id: string;
  type: 'dropdown' | 'toggle' | 'checkbox';
  label: string;
  description?: string;
  icon: string;
  options?: SettingOption[];
  defaultValue: any;
  order: number;
}

// 生成器选择器配置
export interface GeneratorSelectorConfig {
  defaultText: string;
  defaultIcon: string;
  modalTitle: string;
}

// 核心交互区域配置
export interface CoreInteractionConfig {
  // 主输入框配置
  promptInput: {
    defaultPlaceholder: string;
    maxLength: number;
    hint?: string;
  };
  
  // 生成器选择器配置
  generatorSelector: GeneratorSelectorConfig;
  
  // 生成器分类和卡片
  categories: GeneratorCategory[];
  generators: GeneratorCard[];
  
  // 通用设置
  commonSettings: CommonSetting[];
  
  // 操作按钮配置
  actions: {
    clearText: string;
    generateText: string;
    generatingText: string;
    generateTextHint: string;
  };

  default_message: {
    is_generator_selected: string;
    is_prompt_empty: string;
    is_settings_invalid: string;
    generation_failed: string;
    api_request_failed_message_status: string;
    include_meaning_prompt: string;
    no_include_meaning_prompt: string;
    name_length_short_prompt: string;
    name_length_medium_prompt: string;
    name_length_long_prompt: string;
    user_prompt: string;
  };
}

// 核心交互状态
export interface CoreInteractionState {
  // 用户输入
  userPrompt: string;
  
  // 选中的生成器
  selectedGenerator: GeneratorCard | null;
  
  // 设置值
  settings: Record<string, any>;
  
  // UI状态
  isGeneratorModalOpen: boolean;
  isGenerating: boolean;
  results: any[]; // 使用现有的 GeneratedName 类型
  resultsVisible: boolean;
  error?: string;
}

// 生成结果类型（兼容新旧格式）
export interface GenerationResult {
  id: string;
  name: string;
  pronunciation?: string;
  description: string;
  tags?: string[];
}

// 核心交互组件 Props
export interface CoreInteractionZoneProps {
  config: CoreInteractionConfig;
  initialState?: Partial<CoreInteractionState>;
  onGenerate?: (config: CoreInteractionConfig, prompt: string, generator: GeneratorCard | null, settings: Record<string, any>) => Promise<GenerationResult[]>;
  onStateChange?: (state: CoreInteractionState) => void;
  className?: string;
}

// 生成器选择器组件 Props
export interface GeneratorSelectorProps {
  config: GeneratorSelectorConfig;
  selectedGenerator: GeneratorCard | null;
  onClick: () => void;
  className?: string;
}

// 生成器模态框组件 Props
export interface GeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  categories: GeneratorCategory[];
  generators: GeneratorCard[];
  selectedGenerator: GeneratorCard | null;
  onSelect: (generator: GeneratorCard) => void;
}

// 提示词输入组件 Props
export interface PromptInputSectionProps {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  maxLength: number;
  hint?: string;
  disabled?: boolean;
  className?: string;
  settingsBar?: React.ReactNode;
}

// 设置栏组件 Props
export interface SettingsBarProps {
  generatorSelector: React.ReactNode;
  commonSettings: CommonSetting[];
  settingsValues: Record<string, any>;
  onSettingChange: (settingId: string, value: any) => void;
  className?: string;
}

// 通用设置组件 Props
export interface CommonSettingsProps {
  settings: CommonSetting[];
  values: Record<string, any>;
  onChange: (settingId: string, value: any) => void;
  className?: string;
}

// 结果展示组件 Props
export interface ResultsSectionProps {
  results: any[];
  loading: boolean;
  visible: boolean;
  onSave?: (result: any) => void;
  onRegenerate?: () => void;
  className?: string;
}
