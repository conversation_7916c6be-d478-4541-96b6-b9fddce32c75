// 导航相关类型定义

export interface NavigationCategory {
  id: string;
  name: string;
  icon: string;
  order: number;
}

export interface NavigationGenerator {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
  badge?: 'hot' | 'new' | 'popular';
  url: string;
  order: number;
  isActive: boolean;
}

export interface NavigationConfig {
  categories: NavigationCategory[];
  generators: NavigationGenerator[];
}
