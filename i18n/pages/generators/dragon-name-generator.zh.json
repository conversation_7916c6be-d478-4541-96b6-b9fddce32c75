{"seo_content": {"how_to_use": {"title": "如何使用龙名字生成器", "intro": "按照这些简单步骤为你的角色创造完美的龙名字", "step1": {"title": "描述你的龙", "description": "输入关于你的龙的元素、年龄和特征的详细信息", "tip1": "包含具体元素（火、冰、雷电、大地、阴影）", "tip2": "描述年龄类别（远古、成年、幼年、雏龙）", "tip3": "提及性格特征（智慧、凶猛、狡猾、高贵）"}, "step2": {"title": "自定义设置", "description": "选择名字数量、长度和附加功能的偏好设置", "tip1": "生成5-10个名字以获得最佳多样性", "tip2": "选择'中等'长度以获得最真实的结果", "tip3": "启用背景故事功能以获得详细的元素传说和龙族历史"}, "step3": {"title": "生成并保存", "description": "创建你的名字并保存体现你的龙之力量的名字", "tip1": "尝试不同的元素描述以获得多样化的结果", "tip2": "使用收藏功能建立你的龙族收藏", "tip3": "如果想要更多选择可以重新生成"}, "tips_title": "专业提示", "previous": "上一步", "next": "下一步"}, "inspiration_gallery": {"title": "精选龙名字创作", "description": "不知道如何开始使用我们的龙名字生成器？从这些AI锻造的龙名字中获得灵感，每个名字都蕴含着元素力量和古老传说。", "items": [{"name": "永恒烈焰·派罗萨恩", "story": "一条远古红龙，其名字如千座熔炉的咆哮般回响千年。派罗萨恩的火焰炽热到能融化岩石，其智慧跨越千年，这个名字完美适合强大的火龙。", "pronunciation": "派罗萨恩"}, {"name": "冰川之语·格拉西亚利斯", "story": "一条威严的白龙，其吐息能在瞬间冰封整个湖泊。格拉西亚利斯如冬风般穿越山峰，她的名字承载着永恒冰雪的古老力量和冰封领域的寂静。", "pronunciation": "格拉西亚利斯"}, {"name": "风暴召唤者·沃尔萨里昂", "story": "一条蓝龙，其存在本身就能让空气充满电流。沃尔萨里昂掌控雷电本身，他的名字闪烁着风暴的力量和暴风雨的威权，是雷电龙的理想选择。", "pronunciation": "沃尔萨里昂"}, {"name": "暗影之噬·安布拉克西斯", "story": "一条神秘的暗影龙，栖息在光明与黑暗之间。安布拉克西斯如烟雾般穿梭于阴影中，她的名字被那些知晓虚空中潜藏力量的人恐惧地低语。", "pronunciation": "安布拉克西斯"}]}, "why_us": {"title": "为什么我们的龙名字创造器与众不同", "content": "与简单组合随机音节的基础奇幻名字工具不同，我们的AI驱动龙名字生成器深刻理解定义龙族的深层神话传统和元素力量。我们不仅仅创造名字——我们锻造具有古老力量、元素掌控和永恒智慧的传奇身份。每个生成的名字都反映了真正的龙族特征：它们存在的威严、知识的深度，以及与原始力量的联系。我们的龙角色创造器考虑元素亲和力、年龄类别和性格特征等因素，提供与这些传奇生物令人敬畏的本质产生共鸣的名字。"}, "prompt_guide": {"title": "掌握有效龙族提示的艺术", "intro": "释放我们龙名字创造器全部潜力的秘诀在于制作详细、富有神话色彩的提示词。以下是成为提示词大师的方法：", "tips": [{"title": "指定元素和力量", "description": "不要简单地说'火龙'，试试'一条远古红龙，其火焰能融化山脉，其吐息能点燃空气本身'。元素细节帮助我们的奇幻名字工具创造反映其原始力量的名字。"}, {"title": "包含年龄和智慧", "description": "添加年龄类别，如'一条沉睡千年的远古巨龙'或'一条正在发现其元素传承的幼龙'。年龄影响龙名字的庄严感和复杂性。"}, {"title": "描述它们的领域", "description": "提及它们的领土：'统治冰封山峰的龙'或'守护水下宝库的巨龙'。领域和栖息地塑造龙名字的感觉和声音。"}, {"title": "考虑它们的传说", "description": "包含它们的声誉：'被称为王者之祸'或'被低语为风暴使者'。传奇地位帮助打造与其神话存在相匹配的名字。"}]}, "style_exploration": {"title": "探索多样化的龙族命名传统", "intro": "不同神话和元素的龙有着独特的命名模式。我们的龙名字生成器识别这些传统：", "items": [{"style_title": "五彩龙：力量与毁灭", "style_description": "五彩龙（红、蓝、绿、黑、白）通常有反映其破坏性本质和元素掌控的名字。这些名字承载着重量和威胁，经常包含刺耳的辅音和对其元素领域的引用。", "prompt_example": "尝试这样提示：'一条远古红龙，曾经夷平王国，其宝库遍布充满熔金的洞穴。'"}, {"style_title": "金属龙：高贵与智慧", "style_description": "金属龙（金、银、青铜、铜、黄铜）通常拥有反映其高贵本质和古老智慧的名字。这些名字听起来更加悦耳，经常包含对贵金属和天体的引用。", "prompt_example": "对于高贵的金属龙名字，试试：'一条金龙，作为古老知识的守护者和凡人领域的保护者。'"}, {"style_title": "元素龙：原始力量", "style_description": "元素龙体现了超越传统五彩光谱的纯粹元素力量。它们的名字经常反映自然本身的原始力量——风暴、地震、海啸或火山爆发。", "prompt_example": "对于元素龙名字：'一条风暴龙，掌控雷电和雷鸣，栖息在永恒暴风雨的中心。'"}]}, "faq": {"title": "常见问题", "categories": [{"category": "基本使用", "items": [{"question": "如何为我的角色生成更好的龙名字？", "answer": "关键是提供关于你的龙的元素本质、年龄和性格的详细描述。不要只说'火龙'，试试'一条远古红龙，其火焰燃烧了数个世纪，既囤积黄金也囤积知识'。包括元素力量、年龄类别、领域和传奇事迹。"}, {"question": "我可以指定不同的龙类型和元素吗？", "answer": "可以！只需在描述中提及龙的类型和元素。我们的AI理解五彩龙（红、蓝、绿、黑、白）、金属龙（金、银、青铜、铜、黄铜）和元素龙（风暴、阴影、水晶等）。"}, {"question": "一次应该生成多少个名字？", "answer": "我们建议每次生成5-10个名字。这能给你足够的多样性来找到完美的匹配，同时让你比较不同选项，看看我们的AI如何解释你的龙的传奇本质。"}]}, {"category": "名字特征", "items": [{"question": "什么让龙名字听起来如此强大和古老？", "answer": "龙名字遵循特定模式：强辅音、古老的语言根源，以及对元素力量或传奇事迹的引用。我们的AI从广泛的神话和奇幻文学中学习了这些模式，创造真正体现龙族威严的名字。"}, {"question": "不同龙类型有不同的命名风格吗？", "answer": "当然！五彩龙倾向于更威胁性、以力量为焦点的名字；金属龙偏爱高贵、以智慧为基础的名字；元素龙通常有反映原始自然力量的名字。指定龙类型以获得真实的结果。"}, {"question": "为什么龙名字包含元素引用和称号？", "answer": "龙是拥有巨大力量和古老智慧的生物。它们的名字经常反映其元素掌控、领土统治或传奇事迹。我们提供这些细节来帮助你理解你的龙在世界中的地位和其神话意义。"}]}, {"category": "使用权限", "items": [{"question": "我可以在商业项目中使用生成的名字吗？", "answer": "可以！我们的龙名字生成器创造的所有名字都可以在任何项目中免费使用，无论是个人还是商业用途。这包括小说、游戏、艺术作品或任何其他创意项目。"}, {"question": "使用名字时需要注明生成器吗？", "answer": "不需要注明，不过如果创作者提及我们的工具我们总是很感激！名字一旦生成就可以自由使用。"}]}], "related_generators": {"title": "探索更多奇幻名字生成器", "items": [{"title": "D&D名字生成器", "description": "为任何D&D角色生成完美的名字，包含背景故事和文化背景。", "url": "/dnd-name-generator", "icon": "RiSwordLine"}, {"title": "角色名字生成器", "description": "适用于各种背景和设定的通用角色名字生成器。", "url": "/character-name-generator", "icon": "RiUserLine"}, {"title": "城市名字生成器", "description": "为繁华首都、隐秘村庄和神秘定居点生成名字。", "url": "/city-name-generator", "icon": "RiCommunityLine"}]}}}}