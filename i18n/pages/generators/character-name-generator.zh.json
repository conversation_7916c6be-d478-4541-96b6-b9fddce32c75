{"seo_content": {"how_to_use": {"title": "如何使用角色名字生成器", "intro": "按照这些简单步骤为任何故事或设定创造完美的角色名字", "step1": {"title": "描述你的角色", "description": "输入关于你的角色背景、性格和设定的详细信息", "tip1": "包含具体类型（奇幻、科幻、现代、历史）", "tip2": "描述性格特征和角色原型", "tip3": "提及文化背景或时代背景"}, "step2": {"title": "自定义设置", "description": "选择名字数量、长度和附加功能的偏好设置", "tip1": "生成5-10个名字以获得最佳多样性", "tip2": "选择'中等'长度以获得最真实的结果", "tip3": "启用背景故事功能以获得详细的角色概念和起源"}, "step3": {"title": "生成并保存", "description": "创建你的名字并保存完美适合你角色的名字", "tip1": "尝试不同的角色描述以获得多样化的结果", "tip2": "使用收藏功能建立你的角色收藏", "tip3": "如果想要更多选择可以重新生成"}, "tips_title": "专业提示", "previous": "上一步", "next": "下一步"}, "inspiration_gallery": {"title": "精选角色名字创作", "description": "不知道如何开始使用我们的角色名字生成器？从这些AI锻造的角色名字中获得灵感，每个名字都蕴含着独特的个性和引人入胜的背景故事。", "items": [{"name": "艾琳娜·乌鸦峰", "story": "一位意志坚定的奇幻女英雄，拥有贵族血统和神秘的过去。艾琳娜承载着家族传承的重量，同时作为一名熟练的法师和外交官开辟自己的道路，完美适合史诗奇幻冒险。", "pronunciation": "艾琳娜·乌鸦峰"}, {"name": "马库斯·陈", "story": "一位才华横溢的赛博朋克黑客，在反乌托邦未来的霓虹街道中穿行。马库斯将东方智慧与尖端技术相结合，使他成为探索身份和反叛的科幻故事的理想选择。", "pronunciation": "马库斯·陈"}, {"name": "伊莎贝拉·荆棘田", "story": "一位维多利亚时代的侦探，拥有敏锐的智慧和解决谜团的非传统方法。伊莎贝拉挑战社会规范，同时揭开伦敦精英社会最黑暗的秘密。", "pronunciation": "伊莎贝拉·荆棘田"}, {"name": "凯·风暴之风", "story": "一位来自游牧部落的流浪战士，寻求恢复被元素混乱撕裂的世界的平衡。凯的名字既反映了他与自然的联系，也反映了他作为和平使者的命运。", "pronunciation": "凯·风暴之风"}]}, "why_us": {"title": "为什么我们的角色名字创造器与众不同", "content": "与简单随机化音节的基础名字生成器不同，我们的AI驱动角色名字生成器深刻理解名字、性格和故事叙述之间的深层联系。我们不仅仅创造名字——我们打造与真实角色发展和叙事目的产生共鸣的身份。每个生成的名字都反映了对文化背景、类型惯例和角色原型的仔细考虑。我们的角色创造器考虑性格特征、故事设定和文化起源等因素，提供与你的角色本质和在叙事中的角色真正相关的名字。"}, "prompt_guide": {"title": "掌握有效角色提示的艺术", "intro": "释放我们角色名字创造器全部潜力的秘诀在于制作详细、富有故事色彩的提示词。以下是成为提示词大师的方法：", "tips": [{"title": "定义类型和设定", "description": "不要简单地说'英雄'，试试'维多利亚时代伦敦的蒸汽朋克发明家，创造机械奇迹来对抗超自然威胁'。类型和设定帮助我们的奇幻名字工具创造完美适合你世界的名字。"}, {"title": "包含性格和角色", "description": "添加角色特征，如'一个愤世嫉俗的侦探，隐藏着同情心'或'一个乐观的治疗师，在每个人身上都能看到善良'。性格塑造名字的感觉以及它们传达的角色信息。"}, {"title": "描述他们的背景", "description": "提及他们的起源：'在北方荒野被狼群抚养长大'或'出生于贵族但选择了冒险生活'。背景影响命名模式和文化联系。"}, {"title": "考虑他们的旅程", "description": "包含他们的故事弧线：'为过去的错误寻求救赎'或'发现隐藏的魔法能力'。角色发展帮助打造与你的故事一起成长的名字。"}]}, "style_exploration": {"title": "探索多样化的角色命名传统", "intro": "不同的类型、文化和时代有着独特的命名惯例。我们的角色名字生成器识别这些传统：", "items": [{"style_title": "奇幻角色：魔法与神话", "style_description": "奇幻角色名字经常从神话传统、古代语言和魔法概念中汲取灵感。这些名字可能引用自然元素、神秘力量或传奇英雄，创造一种超凡脱俗的感觉，将读者带入魔法领域。", "prompt_example": "尝试这样提示：'一个保护古老森林并与林地精灵交流的半精灵游侠，挥舞着由星光制成的弓。'"}, {"style_title": "现代角色：当代与亲切", "style_description": "现代角色名字反映当前的命名趋势和文化多样性。这些名字感觉熟悉和接地气，帮助读者在当代设定中与角色建立联系，同时仍保持独特性和个性。", "prompt_example": "对于现代名字，试试：'一个来自硅谷的科技企业家，离开企业生活在俄勒冈州农村开始可持续农业倡议。'"}, {"style_title": "历史角色：时代真实性", "style_description": "历史角色名字必须在时代准确性和现代读者的可读性之间取得平衡。这些名字反映其时代的命名惯例，同时对当代读者保持可接受和难忘。", "prompt_example": "对于历史角色：'佛罗伦萨的文艺复兴艺术家，秘密研究解剖学并挑战其时代的艺术惯例。'"}]}, "faq": {"title": "常见问题", "categories": [{"category": "基本使用", "items": [{"question": "如何为我的故事生成更好的角色名字？", "answer": "关键是提供详细的角色描述，包括类型、性格、背景和在故事中的角色。不要只说'战士'，试试'一个寻求和平的伤痕累累的老兵，在多年战争后现在保护一个小村庄免受强盗侵害'。包括设定、性格特征和角色弧线以获得最佳结果。"}, {"question": "我可以指定不同的类型和时代吗？", "answer": "可以！只需在描述中提及类型和设定。我们的AI理解奇幻、科幻、现代、历史、恐怖、浪漫和许多其他类型，调整命名惯例以匹配你故事的世界和氛围。"}, {"question": "一次应该生成多少个名字？", "answer": "我们建议每次生成5-10个名字。这能给你足够的多样性来找到完美的匹配，同时让你比较不同选项，看看我们的AI如何解释你的角色概念。"}]}, {"category": "名字特征", "items": [{"question": "什么让角色名字难忘和真实？", "answer": "优秀的角色名字在独特性和可发音性之间取得平衡，反映角色的性格和背景，并自然地融入他们的故事世界。我们的AI考虑文化真实性、类型惯例和角色心理学，创造既独特又可信的名字。"}, {"question": "不同类型有不同的命名风格吗？", "answer": "当然！奇幻名字经常使用神话元素；科幻名字可能包含未来或技术元素；历史名字反映时代准确性；现代名字遵循当代趋势。指定你的类型以获得真实的结果。"}, {"question": "为什么一些角色名字包含含义和起源？", "answer": "角色名字经常承载增强故事叙述的象征意义。我们提供含义和文化起源，帮助你理解名字如何与你角色的性格、命运或在叙事中的角色相联系。"}]}, {"category": "使用权限", "items": [{"question": "我可以在商业项目中使用生成的名字吗？", "answer": "可以！我们的角色名字生成器创造的所有名字都可以在任何项目中免费使用，无论是个人还是商业用途。这包括小说、游戏、剧本或任何其他创意项目。"}, {"question": "使用名字时需要注明生成器吗？", "answer": "不需要注明，不过如果创作者提及我们的工具我们总是很感激！名字一旦生成就可以自由使用。"}]}], "related_generators": {"title": "探索更多名字生成器", "items": [{"title": "D&D名字生成器", "description": "为任何D&D角色生成完美的名字，包含背景故事和文化背景。", "url": "/dnd-name-generator", "icon": "RiSwordLine"}, {"title": "龙名字生成器", "description": "为古老巨龙和传奇龙族创造威严的名字。", "url": "/dragon-name-generator", "icon": "RiFireLine"}, {"title": "城市名字生成器", "description": "为繁华首都、隐秘村庄和神秘定居点生成名字。", "url": "/city-name-generator", "icon": "RiCommunityLine"}]}}}}