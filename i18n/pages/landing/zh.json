{"template": "nomenus-template-one", "theme": "dark", "header": {"brand": {"title": "Nomenus", "logo": {"src": "/logo.png", "alt": "Nomenus 徽标"}, "url": "/"}, "nav": {"items": [{"title": "首页", "url": "/", "icon": "RiHome3Line"}]}, "buttons": [{"title": "免费开始", "url": "/elf-name-generator", "target": "_self", "variant": "default"}], "show_sign": false, "show_theme": false, "show_locale": true}, "hero": {"title": "终极AI奇幻名字生成器与世界构建工具", "highlight_text": "AI奇幻名字生成器与世界构建工具", "description": "Nomenus是领先的AI奇幻名字生成器和角色名字生成器平台。我们的世界构建工具帮助作家和游戏设计师创造真实的奇幻世界，生成有意义的角色名字和丰富的背景故事。", "buttons": false, "show_happy_users": false, "show_badge": false, "core_interaction": {"promptInput": {"defaultPlaceholder": "请描述您想要创造的角色，如种族、职业、性格特征...", "maxLength": 1000, "hint": "详细的描述能帮助AI生成更符合您需求的名字"}, "generatorSelector": {"defaultText": "选择生成器", "defaultIcon": "wand-magic-sparkles", "modalTitle": "选择AI生成器"}, "categories": [{"id": "all", "name": "全部", "icon": "grid", "order": 1}, {"id": "popular", "name": "热门", "icon": "fire", "order": 2}, {"id": "fantasy", "name": "奇幻", "icon": "crown", "order": 3}, {"id": "scifi", "name": "科幻", "icon": "rocket", "order": 4}, {"id": "modern", "name": "现代", "icon": "building", "order": 5}], "generators": [{"id": "custom", "name": "自定义生成", "icon": "magic-wand", "description": "使用通用AI生成器，适用于各种创意需求", "category": "all", "placeholder": "请描述您想要生成的内容...", "order": 0, "isActive": true, "systemPrompt": "你是一个专业的创意名字生成器。根据用户的描述，生成富有创意和意义的名字。请确保名字符合用户的需求和背景设定。\n\n返回格式：返回一个JSON数组，包含指定数量的名字对象：\n[\n  {\n    \"name\": \"名字\",\n    \"description\": \"详细的含义和背景描述\",\n    \"pronunciation\": \"发音指导（可选）\",\n    \"tags\": [\"标签1\", \"标签2\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "elf-name-generator", "name": "精灵名字生成器", "icon": "crown", "description": "为您的精灵角色生成富有魔法色彩的名字，包含高等精灵、木精灵、暗精灵等不同种族风格", "category": "fantasy", "badge": "hot", "placeholder": "请描述您的精灵角色，如种族（高等精灵/木精灵/暗精灵）、性格、外貌特征...", "order": 1, "isActive": true, "systemPrompt": "你是一个专业的奇幻精灵名字生成器。你精通精灵文化、语言和传统，能够创造出富有魔法色彩和深层含义的精灵名字。\n\n指导原则：\n- 精灵名字应该优雅、神秘，带有自然和魔法的韵味\n- 考虑不同精灵种族的特色：高等精灵（高贵、魔法）、木精灵（自然、森林）、暗精灵（神秘、力量）\n- 名字应该有深层的文化含义和背景故事\n- 包含音韵优美的发音指导\n\n返回格式：返回一个JSON数组，包含指定数量的精灵名字：\n[\n  {\n    \"name\": \"精灵名字\",\n    \"description\": \"详细的含义、起源和背景故事\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"精灵种族\", \"特征标签\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dnd-name-generator", "name": "Dungeons & Dragons名字生成器", "icon": "dice", "description": "生成符合Dungeons & Dragons规则的完整名字，包括精灵、矮人、人类、半精灵等不同种族", "category": "fantasy", "badge": "popular", "placeholder": "请描述您的D&D角色：种族（精灵、矮人、人类、半精灵等）、背景、性格、职业...", "order": 2, "isActive": true, "systemPrompt": "你是一个专业的D&D名字生成器，精通所有主要D&D种族的文化、语言和命名规则。你理解D&D悠久的历史和传统，能够创造出符合D&D规则的完整名字。\n\n种族指导原则：\n- **精灵**: 优雅、音韵优美的名字，反映与魔法和自然的联系（高等精灵：高贵/魔法，木精灵：自然/森林，暗精灵：神秘/力量）\n- **矮人**: 强大、氏族相关的名字，体现荣誉、工艺和山地传统\n- **人类**: 多样化的名字，反映各种文化背景和地区\n- **半精灵/半兽人**: 融合两种文化的名字，反映两种血统的影响\n- **其他种族**: 遵循D&D已有的种族设定，如龙族、地精、半身人等\n\n每个名字应该:\n- 符合D&D的命名规则和背景故事\n- 包含文化意义和含义\n- 反映角色的背景和性格\n- 适合桌面游戏，不显得突兀\n\n返回格式：返回一个JSON数组，包含指定数量的D&D名字：\n[\n  {\n    \"name\": \"角色名字\",\n    \"description\": \"文化意义、种族背景和角色在D&D中的意义\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"种族\", \"文化特征\", \"适合职业\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dwarf-name-generator", "name": "矮人名字生成器", "icon": "sword", "description": "生成具有矮人传统特色的名字，适合战士、工匠、矿工等不同职业的矮人角色", "category": "fantasy", "placeholder": "请描述您的矮人角色，如职业（战士/工匠/矿工）、性格、所属氏族...", "order": 2, "isActive": true, "systemPrompt": "你是一个专业的矮人名字生成器。你深谙矮人文化、传统和氏族历史，能够创造出体现矮人坚韧、荣誉和工艺精神的名字。\n\n指导原则：\n- 矮人名字应该厚重、有力，体现坚韧和荣誉感\n- 考虑不同职业特色：战士（勇猛、战斗）、工匠（技艺、创造）、矿工（坚韧、大地）\n- 名字常与氏族、祖先或成就相关\n- 体现矮人对家族和传统的重视\n\n返回格式：返回一个JSON数组，包含指定数量的矮人名字：\n[\n  {\n    \"name\": \"矮人名字\",\n    \"description\": \"详细的含义、氏族背景和职业特色\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"职业\", \"氏族特征\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dragon-name-generator", "name": "龙族名字生成器", "icon": "fire", "description": "为强大的龙族生成威严的名字，包含古龙、幼龙、各种属性的龙族", "category": "fantasy", "badge": "popular", "placeholder": "请描述您的龙族角色，如属性（火龙/冰龙/雷龙）、年龄、性格特征...", "order": 3, "isActive": true, "systemPrompt": "你是一个专业的龙族名字生成器。你了解龙族的古老传说、元素属性和威严本质，能够创造出体现龙族力量、智慧和神秘的名字。\n\n指导原则：\n- 龙族名字应该威严、古老，体现强大的力量和深邃的智慧\n- 考虑不同元素属性：火龙（炽热、毁灭）、冰龙（寒冷、永恒）、雷龙（迅猛、天威）等\n- 体现龙族的年龄和地位：古龙（古老、睿智）、成年龙（强大、统治）、幼龙（潜力、成长）\n- 名字应该有史诗般的气势和深层的神话含义\n\n返回格式：返回一个JSON数组，包含指定数量的龙族名字：\n[\n  {\n    \"name\": \"龙族名字\",\n    \"description\": \"详细的含义、元素属性和传说背景\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"元素属性\", \"年龄特征\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "spaceship-name-generator", "name": "飞船名字生成器", "icon": "rocket", "description": "为科幻世界的飞船生成富有未来感的名字，包含战舰、探索船、商船等不同类型", "category": "scifi", "badge": "new", "placeholder": "请描述您的飞船，如类型（战舰/探索船/商船）、功能、设计风格...", "order": 4, "isActive": true, "systemPrompt": "你是一个专业的科幻飞船名字生成器。你精通科幻文化、太空探索历史和未来科技概念，能够创造出体现科技感、探索精神和未来愿景的飞船名字。\n\n指导原则：\n- 飞船名字应该具有科技感、未来感，体现人类对太空的探索精神\n- 考虑不同飞船类型：战舰（威武、防御）、探索船（发现、冒险）、商船（贸易、运输）、科研船（知识、创新）\n- 名字可以体现功能特性、设计理念或纪念意义\n- 融合现实科技术语和科幻想象元素\n\n返回格式：返回一个JSON数组，包含指定数量的飞船名字：\n[\n  {\n    \"name\": \"飞船名字\",\n    \"description\": \"详细的含义、设计理念和功能特色\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"飞船类型\", \"特征标签\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "city-name-generator", "name": "城市名字生成器", "icon": "building", "description": "生成现代或幻想世界的城市名字，适合小说、游戏世界构建", "category": "modern", "placeholder": "请描述您的城市，如地理位置、文化背景、城市特色...", "order": 5, "isActive": true, "systemPrompt": "你是一个专业的城市名字生成器。你深谙地理学、历史学、语言学和文化学，能够创造出体现地域特色、历史底蕴和文化内涵的城市名字。\n\n指导原则：\n- 城市名字应该体现地理特征、历史背景或文化特色\n- 考虑不同城市类型：港口城市（贸易、海洋）、山城（地势、防御）、商业中心（繁荣、交通）、文化古城（历史、传统）\n- 名字可以来源于地理特征、历史事件、文化象征或创始人名\n- 既要有现实感又要有想象力，适合世界构建\n\n返回格式：返回一个JSON数组，包含指定数量的城市名字：\n[\n  {\n    \"name\": \"城市名字\",\n    \"description\": \"详细的含义、地理特色和历史背景\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"城市类型\", \"地理特征\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "character-name-generator", "name": "角色名字生成器", "icon": "user", "description": "通用角色名字生成器，适用于各种背景和设定的角色", "category": "popular", "badge": "popular", "placeholder": "请描述您的角色，如性别、年龄、职业、性格特征...", "order": 6, "isActive": true, "systemPrompt": "你是一个专业的角色名字生成器。你精通各种文化背景、历史时期和文学传统，能够为不同设定下的角色创造出合适的名字。\n\n指导原则：\n- 角色名字应该符合角色的背景设定、性格特征和文化环境\n- 考虑不同要素：性别、年龄、职业、性格、出身、时代背景\n- 名字要有个性，能够体现角色的独特性和故事性\n- 适应各种题材：现代、古代、奇幻、科幻、历史等\n- 平衡真实感和创意性\n\n返回格式：返回一个JSON数组，包含指定数量的角色名字：\n[\n  {\n    \"name\": \"角色名字\",\n    \"description\": \"详细的含义、性格特征和背景故事\",\n    \"pronunciation\": \"发音指导\",\n    \"tags\": [\"性别\", \"职业\", \"性格特征\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}], "commonSettings": [{"id": "nameCount", "type": "dropdown", "label": "5个名字", "icon": "hash", "options": [{"value": "3", "label": "3个名字"}, {"value": "5", "label": "5个名字"}, {"value": "8", "label": "8个名字"}], "defaultValue": "5", "order": 1}, {"id": "includeMeaning", "type": "checkbox", "label": "包含背景故事", "description": "为每个名字生成详细的文化含义和背景故事", "icon": "book-open", "order": 2}, {"id": "name<PERSON><PERSON><PERSON>", "type": "dropdown", "label": "适中", "icon": "text", "options": [{"value": "short", "label": "简短"}, {"value": "medium", "label": "适中"}, {"value": "long", "label": "华丽"}], "defaultValue": "medium", "order": 3}], "actions": {"clearText": "清空", "generateText": "生成", "generatingText": "正在生成...", "generateTextHint": "快速生成"}, "default_message": {"is_generator_selected": "请选择一个生成器", "is_prompt_empty": "请输入一个提示词", "is_settings_invalid": "请检查设置", "generation_failed": "生成失败，请重试", "api_request_failed_message_status": "API请求失败状态: {status}, 消息: {message}, 请重试", "include_meaning_prompt": "请描述您想要生成的名字的含义", "no_include_meaning_prompt": "请不要描述您想要生成的名字的含义", "name_length_short_prompt": "生成简短易记的名字（2-4个音节）", "name_length_medium_prompt": "生成中等长度的名字（4-6个音节）", "name_length_long_prompt": "生成华丽的长名字（6-8个音节）", "user_prompt": "请根据以下描述生成 {name_count} 个名字"}}}, "usage": {"name": "usage", "label": "使用指南", "title": "如何使用Nomenus AI奇幻名字生成器", "description": "按照这些简单步骤，使用我们的AI驱动名字生成工具为您的角色、地点和世界创造完美的奇幻名字。", "items": [{"title": "选择生成器", "description": "从我们专业的AI奇幻名字生成器中选择，包括角色名字生成器、龙族名字生成器、精灵名字生成器等。每个生成器都针对特定的奇幻元素进行了优化。", "image": {"src": "/imgs/how-to-use/step1.png", "alt": "选择您的AI奇幻名字生成器"}}, {"title": "描述您的构想", "description": "输入关于您想要创造内容的详细描述。我们的角色名字生成器和世界构建工具在丰富、具体的提示下效果最佳，包括性格、背景和设定细节。", "image": {"src": "/imgs/how-to-use/step2.png", "alt": "描述您的角色或世界元素"}}, {"title": "自定义设置", "description": "调整生成设置，如名字数量、长度和背景故事包含。我们的AI奇幻名字生成器会适应您的偏好，创造完美匹配您创意愿景的名字。", "image": {"src": "/imgs/how-to-use/step3.png", "alt": "自定义您的名字生成设置"}}, {"title": "生成并保存", "description": "创造您的奇幻名字并将喜爱的名字保存到收藏中。使用我们的世界构建工具按项目、战役或故事组织名字，便于访问和一致的世界构建。", "image": {"src": "/imgs/how-to-use/step4.png", "alt": "生成并保存您的奇幻名字"}}]}, "feature": {"name": "feature", "title": "为什么Nomenus是领先的AI奇幻名字生成器", "description": "我们的AI奇幻名字生成器和世界构建工具通过智能的上下文感知生成技术，彻底革新了创意故事创作。", "items": [{"title": "先进的AI奇幻名字生成器", "description": "我们精密的角色名字生成器使用尖端AI技术理解文化背景、语言模式和奇幻传统。通过我们专业的AI奇幻名字生成器生成真实的精灵名字、矮人名字、龙族名字等，每个名字都富有意义和深度。", "icon": "RiSparkling2Line"}, {"title": "全面的世界构建工具", "description": "除了角色名字，Nomenus还是您完整的世界构建工具。创建相互关联的奇幻世界，保持一致的命名规则、文化背景和丰富的传说，提升您的故事创作和游戏设计项目。", "icon": "RiBookOpenLine"}, {"title": "智能角色名字生成器", "description": "我们的角色名字生成器适应您的具体需求，无论您是为桌游RPG创建NPC、为小说创造主角，还是为游戏世界构建整个种族。每个生成的名字都附带背景故事建议和文化背景。", "icon": "RiLayoutGridFill"}, {"title": "有序的世界构建工作流", "description": "Nomenus世界构建工具帮助您在创意项目中保持一致性。保存生成的名字，按战役或故事分类整理，通过我们系统化的世界创建方法构建全面的奇幻宇宙。", "icon": "RiSave3Line"}, {"title": "社区驱动的奇幻创作", "description": "加入数千名使用Nomenus AI奇幻名字生成器和世界构建工具的创作者。分享您生成的内容，从其他创作者那里发现灵感，在我们的创意社区内协作进行奇幻世界构建项目。", "icon": "RiTeamLine"}, {"title": "专业级创意工具", "description": "无论您是专业游戏开发者、出版作家还是热情的爱好者，Nomenus都提供企业级AI奇幻名字生成器功能和世界构建工具，旨在增强您的创意工作流程和故事创作过程。", "icon": "RiPenNibFill"}]}, "stats": {"name": "stats", "label": "统计", "title": "受到全球奇幻创作者信赖", "description": "Nomenus AI奇幻名字生成器和世界构建工具为从独立开发者到大型工作室的创意项目提供支持。", "icon": "FaRegHeart", "items": [{"title": "活跃创作者", "label": "10,000+", "description": "每日使用我们的角色名字生成器"}, {"title": "AI奇幻生成器", "label": "20+", "description": "专业世界构建工具"}, {"title": "生成名字数量", "label": "1M+", "description": "通过Nomenus平台"}]}, "testimonial": {"name": "testimonial", "label": "用户评价", "title": "Nomenus AI奇幻名字生成器用户成功案例", "description": "了解全球创作者如何使用Nomenus角色名字生成器和世界构建工具来增强他们的奇幻项目。", "icon": "GoThumbsup", "items": [{"title": "艾拉拉·万斯", "label": "奇幻小说家", "description": "Nomenus AI奇幻名字生成器彻底改变了我的写作过程。每个角色名字生成器的结果都附带丰富的背景故事，启发了我奇幻小说中的整个章节。这个世界构建工具对任何认真的奇幻作家来说都是必不可少的。", "image": {"src": "/imgs/users/1.png"}}, {"title": "“神鹰计划”团队", "label": "独立游戏工作室", "description": "我们使用Nomenus角色名字生成器在几小时内而不是几周内创建了整个RPG的命名系统。AI奇幻名字生成器在我们的奇幻世界中保持完美的一致性，使我们的世界构建工具工作流程极其高效。", "image": {"src": "/imgs/users/2.png"}}, {"title": "马库斯·索恩", "label": "桌游世界构建师", "description": "Nomenus世界构建工具革命性地改变了我的战役创建。AI奇幻名字生成器产生真实的角色名字，而全面的世界构建工具帮助我在多个奇幻领域和故事线中保持一致性。", "image": {"src": "/imgs/users/3.png"}}, {"title": "索菲亚·雷耶斯", "label": "科幻编剧", "description": "虽然主要以AI奇幻名字生成器闻名，但Nomenus在科幻内容方面也表现出色。角色名字生成器创造了完美的外星人名字，世界构建工具帮助为我的剧本建立了可信的行星系统。", "image": {"src": "/imgs/users/4.png"}}, {"title": "詹姆斯·工坊", "label": "首席游戏设计师", "description": "Nomenus AI奇幻名字生成器和世界构建工具已成为我们开发流程的重要组成部分。角色名字生成器确保风格一致性，而世界构建工具在我们的奇幻游戏宇宙中保持叙事连贯性。", "image": {"src": "/imgs/users/5.png"}}, {"title": "陈安娜", "label": "业余创作者", "description": "作为一名业余奇幻作家，Nomenus在易于使用的水平上提供专业级工具。AI奇幻名字生成器和世界构建工具让我的个人项目感觉精致而真实，显著提升了我的创意故事创作。", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于Nomenus AI奇幻名字生成器的常见问题", "description": "关于使用我们的AI奇幻名字生成器、角色名字生成器和世界构建工具，您需要了解的一切。", "items": [{"title": "什么让Nomenus成为最佳AI奇幻名字生成器？", "description": "Nomenus结合先进的AI技术与对奇幻文学和游戏传统的深度理解。我们的AI奇幻名字生成器不只是创建随机组合——它生成具有文化背景的真实角色名字，使其成为最精密的角色名字生成器和世界构建工具，专为奇幻创作者而设。"}, {"title": "Nomenus角色名字生成器与基础名字生成器有何不同？", "description": "与简单的随机生成器不同，我们的角色名字生成器使用AI理解语言模式、文化背景和奇幻惯例。AI奇幻名字生成器考虑您的具体提示和世界构建要求，创造在您的奇幻设定中感觉真实且有意义的名字。"}, {"title": "谁应该使用Nomenus世界构建工具和AI奇幻名字生成器？", "description": "Nomenus服务于奇幻作家、游戏开发者、桌游主持人、编剧和业余世界构建者。无论您需要为小说主角使用角色名字生成器，还是为游戏宇宙使用全面的世界构建工具，Nomenus都提供专业级AI奇幻名字生成器功能。"}, {"title": "Nomenus AI奇幻名字生成器可以免费使用吗？", "description": "是的！Nomenus提供慷慨的免费套餐，包括访问我们核心AI奇幻名字生成器和角色名字生成器功能。免费用户可以通过我们的世界构建工具每日生成名字。高级计划解锁无限生成和高级世界构建功能。"}, {"title": "我可以保存角色名字生成器的名字吗？", "description": "当然可以！创建免费Nomenus账户来保存您喜爱的AI奇幻名字生成器结果。我们的世界构建工具包括项目组织功能，允许您在一个集中的创意工作空间中分类和管理所有生成的角色名字、地点和传说。"}, {"title": "我可以在商业项目中使用AI奇幻名字生成器内容吗？", "description": "是的！通过Nomenus AI奇幻名字生成器、角色名字生成器和世界构建工具生成的所有内容都归您自由使用，无论是个人还是商业项目。我们鼓励创作者在书籍、游戏和其他创意作品中将他们的Nomenus生成的奇幻世界变为现实。"}]}, "cta": {"name": "cta", "title": "开始使用Nomenus AI奇幻名字生成器创作", "description": "体验我们的AI奇幻名字生成器、角色名字生成器和世界构建工具的强大功能。加入数千名信赖Nomenus满足奇幻世界构建需求的创作者。", "buttons": [{"title": "体验AI奇幻名字生成器", "url": "/", "target": "_self", "icon": "GoArrowUpRight"}]}, "footer": {"name": "footer", "brand": {"title": "Nomenus", "description": "领先的AI奇幻名字生成器和世界构建工具平台。Nomenus为全球奇幻创作者、游戏开发者和故事创作者提供先进的角色名字生成器功能和全面的世界构建解决方案。", "logo": {"src": "/logo.png", "alt": "Nomenus AI奇幻名字生成器"}, "url": "/"}, "copyright": "© 2025 • Nomenus. 保留所有权利。", "nav": {"items": [{"title": "资源", "children": [{"title": "首页", "url": "/", "target": "_self"}]}, {"title": "公司", "children": [{"title": "联系我们", "url": "mailto:<EMAIL>", "target": "_self"}]}]}, "social": {"items": [{"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_blank"}]}, "agreement": {"items": [{"title": "隐私政策", "url": "/privacy-policy"}, {"title": "服务条款", "url": "/terms-of-service"}]}}}