{"template": "nomenus-template-one", "theme": "dark", "header": {"brand": {"title": "Nomenus", "logo": {"src": "/logo.png", "alt": "No<PERSON>us <PERSON>"}, "url": "/"}, "nav": {"items": [{"title": "Home", "url": "/", "icon": "RiHome3Line"}]}, "buttons": [{"title": "Start for Free", "url": "/", "target": "_self", "variant": "default"}], "show_sign": false, "show_theme": false, "show_locale": true}, "hero": {"title": "The Ultimate AI Fantasy Name Generator & World-Building Tool", "highlight_text": "AI Fantasy Name Generator & World-Building Tool", "description": "Generate fantasy names for your fantasy world", "buttons": false, "show_happy_users": false, "show_badge": false, "core_interaction": {"promptInput": {"defaultPlaceholder": "Describe the character you want to create, such as race, profession, personality traits...", "maxLength": 1000, "hint": "Detailed descriptions help AI generate names that better match your needs"}, "generatorSelector": {"defaultText": "Select Generator", "defaultIcon": "wand-magic-sparkles", "modalTitle": "Choose AI Generator"}, "categories": [{"id": "all", "name": "All", "icon": "grid", "order": 1}, {"id": "popular", "name": "Popular", "icon": "fire", "order": 2}, {"id": "fantasy", "name": "Fantasy", "icon": "crown", "order": 3}, {"id": "scifi", "name": "Sci-Fi", "icon": "rocket", "order": 4}, {"id": "modern", "name": "Modern", "icon": "building", "order": 5}], "generators": [{"id": "custom", "name": "Custom Generator", "icon": "magic-wand", "description": "Use universal AI generator for various creative needs", "category": "all", "placeholder": "Describe what you want to generate...", "order": 0, "isActive": true, "systemPrompt": "You are a professional creative name generator. Based on the user's description, generate creative and meaningful names. Please ensure the names match the user's requirements and background settings.\n\nReturn format: Return a JSON array containing the specified number of name objects:\n[\n  {\n    \"name\": \"Name\",\n    \"description\": \"Detailed meaning and background description\",\n    \"pronunciation\": \"Pronunciation guide (optional)\",\n    \"tags\": [\"tag1\", \"tag2\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "elf-name-generator", "name": "Elf Name Generator", "icon": "crown", "description": "Generate magical elf names for your characters, including <PERSON> Elves, Wood Elves, Dark Elves and other racial styles", "category": "fantasy", "badge": "hot", "placeholder": "Describe your elf character, such as race (High Elf/Wood Elf/Dark Elf), personality, appearance...", "order": 1, "isActive": true, "systemPrompt": "You are a professional fantasy elf name generator. You are well-versed in elven culture, languages, and traditions, capable of creating magical and deeply meaningful elf names.\n\nGuidelines:\n- Elf names should be elegant, mysterious, with natural and magical charm\n- Consider different elf races: <PERSON> Elves (noble, magical), <PERSON> (natural, forest), Dark <PERSON> (mysterious, powerful)\n- Names should have deep cultural meaning and background stories\n- Include melodious pronunciation guides\n\nReturn format: Return a JSON array containing the specified number of elf names:\n[\n  {\n    \"name\": \"Elf Name\",\n    \"description\": \"Detailed meaning, origin and background story\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"elf race\", \"characteristic tags\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dnd-name-generator", "name": "D&D Name Generator", "icon": "dice", "description": "Generate authentic names for all major Dungeons & Dragons races - from noble Elves to sturdy Dwarves, mysterious T<PERSON>lings to brave Dragonborn", "category": "fantasy", "badge": "popular", "placeholder": "Describe your D&D character: race (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc.), background, personality, class...", "order": 1, "isActive": true, "systemPrompt": "You are a master Dungeons & Dragons name generator with deep knowledge of all major D&D races, their cultures, languages, and naming conventions. You understand the rich lore and traditions that have made D&D the most beloved tabletop RPG for over 45 years.\n\nRace Guidelines:\n- **Elves**: Elegant, melodious names reflecting their connection to magic and nature (High Elves: noble/magical, Wood Elves: natural/forest, Dark Elves: mysterious/powerful)\n- **Dwarves**: Strong, clan-based names reflecting honor, craftsmanship, and mountain heritage\n- **Humans**: Diverse names reflecting various cultural backgrounds and regions\n- **Halflings**: Warm, friendly names often related to comfort, food, or nature\n- **Dragonborn**: Powerful names reflecting draconic heritage and elemental affinities\n- **Tieflings**: Names that may reflect infernal heritage or chosen virtue names\n- **Gnomes**: Inventive names often reflecting curiosity, tinkering, or forest connections\n- **Half-Elves/Half-Orcs**: Names blending cultural influences from both heritages\n- **Other races**: Aarakocra, Genasi, Goliath, etc. - follow established D&D lore\n\nEach name should:\n- Follow official D&D naming conventions and lore\n- Include cultural significance and meaning\n- Reflect the character's potential background and personality\n- Be suitable for tabletop gaming without standing out awkwardly\n\nReturn format: Return a JSON array containing the specified number of D&D character names:\n[\n  {\n    \"name\": \"Character Name\",\n    \"description\": \"Cultural meaning, racial background, and character significance within D&D lore\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"race\", \"cultural traits\", \"suitable classes\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dwarf-name-generator", "name": "Dwarf Name Generator", "icon": "sword", "description": "Generate traditional dwarf names suitable for warriors, craftsmen, miners and other dwarf professions", "category": "fantasy", "placeholder": "Describe your dwarf character, such as profession (warrior/craftsman/miner), personality, clan...", "order": 2, "isActive": true, "systemPrompt": "You are a professional dwarf name generator. You deeply understand dwarf culture, traditions, and clan history, capable of creating names that embody dwarven resilience, honor, and craftsmanship spirit.\n\nGuidelines:\n- Dwarf names should be strong and powerful, reflecting resilience and honor\n- Consider different professions: warriors (brave, combat), craftsmen (skill, creation), miners (endurance, earth)\n- Names often relate to clans, ancestors, or achievements\n- Reflect dwarven values of family and tradition\n\nReturn format: Return a JSON array containing the specified number of dwarf names:\n[\n  {\n    \"name\": \"Dwarf Name\",\n    \"description\": \"Detailed meaning, clan background and professional characteristics\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"profession\", \"clan characteristics\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "dragon-name-generator", "name": "Dragon Name Generator", "icon": "fire", "description": "Generate majestic names for powerful dragons, including ancient dragons, young dragons, and various elemental types", "category": "fantasy", "badge": "popular", "placeholder": "Describe your dragon character, such as element (fire/ice/lightning), age, personality traits...", "order": 3, "isActive": true, "systemPrompt": "You are a professional dragon name generator. You understand ancient dragon legends, elemental attributes, and the majestic nature of dragons, capable of creating names that embody dragon power, wisdom, and mystery.\n\nGuidelines:\n- Dragon names should be majestic, ancient, reflecting great power and profound wisdom\n- Consider different elemental attributes: Fire Dragons (blazing, destruction), Ice Dragons (cold, eternal), Lightning Dragons (swift, divine power), etc.\n- Reflect dragon age and status: Ancient Dragons (old, wise), Adult Dragons (powerful, ruling), Young Dragons (potential, growth)\n- Names should have epic grandeur and deep mythological meaning\n\nReturn format: Return a JSON array containing the specified number of dragon names:\n[\n  {\n    \"name\": \"Dragon Name\",\n    \"description\": \"Detailed meaning, elemental attributes and legendary background\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"elemental attribute\", \"age characteristics\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "spaceship-name-generator", "name": "Spaceship Name Generator", "icon": "rocket", "description": "Generate futuristic spaceship names for sci-fi worlds, including warships, exploration vessels, merchant ships", "category": "scifi", "badge": "new", "placeholder": "Describe your spaceship, such as type (warship/explorer/merchant), function, design style...", "order": 4, "isActive": true, "systemPrompt": "You are a professional sci-fi spaceship name generator. You are well-versed in sci-fi culture, space exploration history, and future technology concepts, capable of creating spaceship names that embody technological advancement, exploration spirit, and futuristic vision.\n\nGuidelines:\n- Spaceship names should have a technological, futuristic feel, reflecting humanity's spirit of space exploration\n- Consider different ship types: Warships (mighty, defensive), Exploration vessels (discovery, adventure), Merchant ships (trade, transport), Research ships (knowledge, innovation)\n- Names can reflect functional characteristics, design philosophy, or commemorative significance\n- Blend real technological terminology with sci-fi imagination elements\n\nReturn format: Return a JSON array containing the specified number of spaceship names:\n[\n  {\n    \"name\": \"Spaceship Name\",\n    \"description\": \"Detailed meaning, design philosophy and functional characteristics\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"ship type\", \"characteristic tags\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "city-name-generator", "name": "City Name Generator", "icon": "building", "description": "Generate modern or fantasy city names, perfect for novel and game world building", "category": "modern", "placeholder": "Describe your city, such as location, cultural background, city characteristics...", "order": 5, "isActive": true, "systemPrompt": "You are a professional city name generator. You are well-versed in geography, history, linguistics, and cultural studies, capable of creating city names that reflect regional characteristics, historical heritage, and cultural connotations.\n\nGuidelines:\n- City names should reflect geographical features, historical background, or cultural characteristics\n- Consider different city types: Port cities (trade, ocean), Mountain cities (terrain, defense), Commercial centers (prosperity, transportation), Cultural ancient cities (history, tradition)\n- Names can derive from geographical features, historical events, cultural symbols, or founder names\n- Balance realism with imagination, suitable for world building\n\nReturn format: Return a JSON array containing the specified number of city names:\n[\n  {\n    \"name\": \"City Name\",\n    \"description\": \"Detailed meaning, geographical features and historical background\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"city type\", \"geographical features\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}, {"id": "character-name-generator", "name": "Character Name Generator", "icon": "user", "description": "Universal character name generator suitable for characters of various backgrounds and settings", "category": "popular", "badge": "popular", "placeholder": "Describe your character, such as gender, age, profession, personality traits...", "order": 6, "isActive": true, "systemPrompt": "You are a professional character name generator. You are well-versed in various cultural backgrounds, historical periods, and literary traditions, capable of creating suitable names for characters in different settings.\n\nGuidelines:\n- Character names should match the character's background setting, personality traits, and cultural environment\n- Consider different elements: gender, age, profession, personality, origin, historical background\n- Names should have personality and reflect the character's uniqueness and story potential\n- Adapt to various genres: modern, ancient, fantasy, sci-fi, historical, etc.\n- Balance realism with creativity\n\nReturn format: Return a JSON array containing the specified number of character names:\n[\n  {\n    \"name\": \"Character Name\",\n    \"description\": \"Detailed meaning, personality traits and background story\",\n    \"pronunciation\": \"Pronunciation guide\",\n    \"tags\": [\"gender\", \"profession\", \"personality traits\"]\n  }\n]", "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]}], "commonSettings": [{"id": "nameCount", "type": "dropdown", "label": "5 Names", "icon": "hash", "options": [{"value": "3", "label": "3 Names"}, {"value": "5", "label": "5 Names"}, {"value": "8", "label": "8 Names"}], "defaultValue": "5", "order": 1}, {"id": "includeMeaning", "type": "checkbox", "label": "Include Backstory", "description": "Generate detailed cultural meanings and background stories for each name", "icon": "book-open", "order": 2}, {"id": "name<PERSON><PERSON><PERSON>", "type": "dropdown", "label": "Medium", "icon": "text", "options": [{"value": "short", "label": "Short"}, {"value": "medium", "label": "Medium"}, {"value": "long", "label": "Elaborate"}], "defaultValue": "medium", "order": 3}], "actions": {"clearText": "Clear", "generateText": "Generate", "generatingText": "Generating...", "generateTextHint": "Quickly generate"}, "default_message": {"is_generator_selected": "Please select a generator", "is_prompt_empty": "Please enter a prompt", "is_settings_invalid": "Please check the settings", "generation_failed": "Generation failed, please try again", "api_request_failed_message_status": "API request failed status: {status}, message: {message}, please try again", "include_meaning_prompt": "Please describe the meaning of the name you want to generate", "no_include_meaning_prompt": "Please do not describe the meaning of the name you want to generate", "name_length_short_prompt": "Generate short and easy-to-remember names (2-4 syllables)", "name_length_medium_prompt": "Generate medium-length names (4-6 syllables)", "name_length_long_prompt": "Generate elaborate names (6-8 syllables)", "user_prompt": "Please generate {name_count} names based on the following description"}}}, "usage": {"name": "usage", "label": "How to Use", "title": "How to Use Nomenus AI Fantasy Name Generator", "description": "Follow these simple steps to create perfect fantasy names for your characters, places, and worlds with our AI-powered name generation tools.", "items": [{"title": "Choose Your Generator", "description": "Select from our specialized AI Fantasy Name Generators including Character Name Generator, Dragon Name Generator, Elf Name Generator, and more. Each generator is optimized for specific fantasy elements.", "image": {"src": "/imgs/how-to-use/step1.png", "alt": "Choose your AI Fantasy Name Generator"}}, {"title": "Describe Your Vision", "description": "Enter detailed descriptions about what you want to create. Our Character Name Generator and World-Building Tool work best with rich, specific prompts that include personality, background, and setting details.", "image": {"src": "/imgs/how-to-use/step2.png", "alt": "Describe your character or world element"}}, {"title": "Customize Settings", "description": "Adjust generation settings like name quantity, length, and backstory inclusion. Our AI Fantasy Name Generator adapts to your preferences to create names that perfectly match your creative vision.", "image": {"src": "/imgs/how-to-use/step3.png", "alt": "Customize your name generation settings"}}, {"title": "Generate & Save", "description": "Create your fantasy names and save favorites to your collection. Use our World-Building Tool to organize names by projects, campaigns, or stories for easy access and consistent world-building.", "image": {"src": "/imgs/how-to-use/step4.png", "alt": "Generate and save your fantasy names"}}]}, "feature": {"name": "feature", "title": "Why <PERSON><PERSON><PERSON> is the Leading AI Fantasy Name Generator", "description": "Our AI Fantasy Name Generator and World-Building Tool revolutionizes creative storytelling with intelligent, context-aware generation that goes beyond simple randomization.", "items": [{"title": "Advanced AI Fantasy Name Generator", "description": "Our sophisticated Character Name Generator uses cutting-edge AI to understand cultural contexts, linguistic patterns, and fantasy traditions. Generate authentic elf names, dwarf names, dragon names, and more with our specialized AI Fantasy Name Generator that creates names with meaning and depth.", "icon": "RiSparkling2Line"}, {"title": "Comprehensive World-Building Tool", "description": "Beyond character names, Nomenus serves as your complete World-Building Tool. Create interconnected fantasy worlds with consistent naming conventions, cultural backgrounds, and rich lore that enhances your storytelling and game design projects.", "icon": "RiBookOpenLine"}, {"title": "Intelligent Character Name Generator", "description": "Our Character Name Generator adapts to your specific needs, whether you're creating NPCs for tabletop RPGs, protagonists for novels, or entire populations for game worlds. Each generated name comes with backstory suggestions and cultural context.", "icon": "RiLayoutGridFill"}, {"title": "Organized World-Building Workflow", "description": "Nomenus World-Building Tool helps you maintain consistency across your creative projects. Save generated names, organize them by campaigns or stories, and build comprehensive fantasy universes with our systematic approach to world creation.", "icon": "RiSave3Line"}, {"title": "Community-Driven Fantasy Creation", "description": "Join thousands of creators using Nomenus AI Fantasy Name Generator and World-Building Tool. Share your generated content, discover inspiration from other creators, and collaborate on fantasy world-building projects within our creative community.", "icon": "RiTeamLine"}, {"title": "Professional-Grade Creative Tools", "description": "Whether you're a professional game developer, published author, or passionate hobbyist, Nomenus provides enterprise-level AI Fantasy Name Generator capabilities and World-Building Tools designed to enhance your creative workflow and storytelling process.", "icon": "RiPenNibFill"}]}, "stats": {"name": "stats", "label": "Stats", "title": "Trusted by Fantasy Creators Worldwide", "description": "Nomenus AI Fantasy Name Generator and World-Building Tool powers creative projects from indie developers to major studios.", "icon": "FaRegHeart", "items": [{"title": "Active Creators", "label": "10,000+", "description": "Using Our Character Name Generator Daily"}, {"title": "AI Fantasy Generators", "label": "20+", "description": "Specialized World-Building Tools"}, {"title": "Names Generated", "label": "1M+", "description": "Through Nomenus Platform"}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "Success Stories from Nomenus AI Fantasy Name Generator Users", "description": "Discover how creators worldwide use Nomenus Character Name Generator and World-Building Tool to enhance their fantasy projects.", "icon": "GoThumbsup", "items": [{"title": "<PERSON><PERSON>", "label": "Fantasy Author", "description": "The Nomenus AI Fantasy Name Generator transformed my writing process. Every Character Name Generator result comes with rich backstories that have inspired entire chapters in my fantasy novels. This World-Building Tool is essential for any serious fantasy writer.", "image": {"src": "/imgs/users/1.png"}}, {"title": "Project Condor Team", "label": "Indie Game Studio", "description": "We used Nomenus Character Name Generator to create our entire RPG's naming system in hours instead of weeks. The AI Fantasy Name Generator maintains perfect consistency across our fantasy world, making our World-Building Tool workflow incredibly efficient.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "TTRPG World-Builder", "description": "Nomenus World-Building Tool revolutionized my campaign creation. The AI Fantasy Name Generator produces authentic character names while the comprehensive World-Building Tool helps me maintain consistency across multiple fantasy realms and storylines.", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON>", "label": "Sci-Fi Screenwriter", "description": "While primarily known as an AI Fantasy Name Generator, <PERSON><PERSON><PERSON> excels at sci-fi content too. The Character Name Generator created perfect alien names and the World-Building Tool helped establish believable planetary systems for my screenplay.", "image": {"src": "/imgs/users/4.png"}}, {"title": "James Workshop", "label": "Lead Game Designer", "description": "Nomenus AI Fantasy Name Generator and World-Building Tool have become integral to our development pipeline. The Character Name Generator ensures stylistic consistency while the World-Building Tool maintains narrative coherence across our fantasy game universe.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Hobbyist Creator", "description": "As a hobbyist fantasy writer, <PERSON><PERSON><PERSON> provides professional-grade tools at an accessible level. The AI Fantasy Name Generator and World-Building Tool make my personal projects feel polished and authentic, elevating my creative storytelling significantly.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About Nomenus AI Fantasy Name Generator", "description": "Everything you need to know about using our AI Fantasy Name Generator, Character Name Generator, and World-Building Tool.", "items": [{"title": "What makes Nomenus the best AI Fantasy Name Generator?", "description": "Nomenus combines advanced AI technology with deep understanding of fantasy literature and gaming traditions. Our AI Fantasy Name Generator doesn't just create random combinations - it generates authentic character names with cultural context, making it the most sophisticated Character Name Generator and World-Building Tool available for fantasy creators."}, {"title": "How does the Nomenus Character Name Generator differ from basic name generators?", "description": "Unlike simple random generators, our Character Name Generator uses AI to understand linguistic patterns, cultural contexts, and fantasy conventions. The AI Fantasy Name Generator considers your specific prompts and world-building requirements to create names that feel authentic and meaningful within your fantasy setting."}, {"title": "Who should use Nomenus World-Building Tool and AI Fantasy Name Generator?", "description": "Nomenus serves fantasy authors, game developers, tabletop RPG masters, screenwriters, and hobbyist world-builders. Whether you need a Character Name Generator for your novel's protagonists or a comprehensive World-Building Tool for your game universe, Nomenus provides professional-grade AI Fantasy Name Generator capabilities."}, {"title": "Is the Nomenus AI Fantasy Name Generator free to use?", "description": "Yes! Nomenus offers a generous free tier that includes access to our core AI Fantasy Name Generator and Character Name Generator features. Free users can generate names daily with our World-Building Tool. Premium plans unlock unlimited generations and advanced world-building features."}, {"title": "Can I save names from the Character Name Generator?", "description": "Absolutely! Create a free Nomenus account to save your favorite AI Fantasy Name Generator results. Our World-Building Tool includes project organization features, allowing you to categorize and manage all your generated character names, locations, and lore in one centralized creative workspace."}, {"title": "Can I use AI Fantasy Name Generator content in commercial projects?", "description": "Yes! All content generated through Nomenus AI Fantasy Name Generator, Character Name Generator, and World-Building Tool is yours to use freely in both personal and commercial projects. We encourage creators to bring their Nomenus-generated fantasy worlds to life in books, games, and other creative works."}]}, "cta": {"name": "cta", "title": "Start Creating with Nomenus AI Fantasy Name Generator", "description": "Experience the power of our AI Fantasy Name Generator, Character Name Generator, and World-Building Tool. Join thousands of creators who trust Nomenus for their fantasy world-building needs.", "buttons": [{"title": "Try AI Fantasy Name Generator", "url": "/", "target": "_self", "icon": "GoArrowUpRight"}]}, "footer": {"name": "footer", "brand": {"title": "Nomenus", "description": "Generate fantasy names for your fantasy world", "logo": {"src": "/logo.png", "alt": "Nomenus AI Fantasy Name Generator"}, "url": "/"}, "copyright": "© 2025 • Nomenus. All rights reserved.", "nav": {"items": [{"title": "Resources", "children": [{"title": "Home", "url": "/", "target": "_self"}]}, {"title": "Company", "children": [{"title": "Contact", "url": "mailto:<EMAIL>", "target": "_self"}]}]}, "social": {"items": [{"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_blank"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}