CREATE TABLE nomenus_users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at timestamptz,
    nickname VA<PERSON><PERSON><PERSON>(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50),
    signin_type VARCHAR(50),
    signin_ip VARCHAR(255),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    invite_code VARCHAR(255) NOT NULL default '',
    updated_at timestamptz,
    invited_by VA<PERSON><PERSON><PERSON>(255) NOT NULL default '',
    is_affiliate BOOLEAN NOT NULL default false,
    UNIQUE (email, signin_provider)
);

-- 名字生成记录表
CREATE TABLE nomenus_name_generations (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    user_uuid VARCHAR(36) REFERENCES nomenus_users(uuid),
    generator_type VARCHAR(50) NOT NULL, -- 'elf', 'dwarf', 'city' 等
    user_prompt TEXT NOT NULL,
    parameters JSONB, -- 存储用户选择的参数(性别、复杂度等)
    results JSONB NOT NULL, -- 存储生成的名字列表
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户收藏的名字表
CREATE TABLE nomenus_saved_names (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    user_uuid VARCHAR(36) REFERENCES nomenus_users(uuid),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    generator_type VARCHAR(50) NOT NULL,
    source_generation_uuid VARCHAR(36) REFERENCES nomenus_name_generations(uuid),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 生成器配置表(用于后台管理)
CREATE TABLE nomenus_generators (
    id BIGSERIAL PRIMARY KEY,
    type VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    prompt_template TEXT NOT NULL,
    parameters JSONB, -- 支持的参数配置
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 添加索引
CREATE INDEX idx_name_generations_user_uuid ON nomenus_name_generations(user_uuid);
CREATE INDEX idx_name_generations_type ON nomenus_name_generations(generator_type);
CREATE INDEX idx_saved_names_user_uuid ON nomenus_saved_names(user_uuid);
CREATE INDEX idx_saved_names_type ON nomenus_saved_names(generator_type);