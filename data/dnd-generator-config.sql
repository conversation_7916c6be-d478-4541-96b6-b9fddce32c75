-- D&D Name Generator 基础配置插入
-- 这是一个临时配置，主要为了让页面能够显示 SEO 内容
-- 后续可以完善功能时再更新

INSERT INTO nomenus_generators (
  type, 
  title, 
  description, 
  prompt_template, 
  parameters, 
  is_active
) VALUES (
  'dnd-name-generator',
  'D&D Character Name Generator',
  'Generate authentic D&D character names for your tabletop adventures. Create memorable names for humans, elves, dwarves, and all other D&D races.',
  'You are an expert D&D character name generator. Generate 4 authentic character names based on the following description: {user_prompt}

Consider the character''s race, class, background, and personality. Each name should feel authentic to D&D lore and include a meaningful backstory.

Return exactly 4 names in this JSON format:
[
  {
    "name": "Character Name",
    "description": "A rich backstory explaining the character''s background, significance, and role",
    "pronunciation": "phonetic guide if needed",
    "meaning": "meaning of the name",
    "origin": "cultural or racial origin"
  }
]',
  '{
    "controls": [
      {
        "key": "race",
        "label_t": "race.label",
        "type": "select",
        "options_t": "race.options"
      },
      {
        "key": "gender",
        "label_t": "gender.label", 
        "type": "select",
        "options_t": "gender.options"
      }
    ],
    "user_prompt": {
      "label_t": "prompt.label",
      "placeholder_t": "prompt.placeholder",
      "hint_t": "prompt.hint"
    }
  }',
  true
); 