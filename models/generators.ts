import { GeneratorConfig, SavedName } from "@/types/naming";
import { getSupabaseClient } from "./db";

export async function getSavedNames(user_uuid: string, generator_type: string): Promise<SavedName[]> {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
        .from('nomenus_saved_names')
        .select('*')
        .eq('user_uuid', user_uuid)
        .eq('generator_type', generator_type)
        .order('created_at', { ascending: false });

    if (error) {
        console.error('Failed to load saved names:', error);
        return [];
    }
    return data || [];
}

export async function getGeneratorConfig(type: string): Promise<GeneratorConfig | null> {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('nomenus_generators')
      .select('*')
      .eq('type', type)
      .eq('is_active', true)
      .single<GeneratorConfig>();
  
    if (error) {
      console.error(`Error fetching generator config for type "${type}":`, error);
      return null;
    }
  
    return data;
  }
  
export async function getAllGeneratorTypes(): Promise<{ type: string }[]> {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
        .from('nomenus_generators')
        .select('type')
        .eq('is_active', true);   

    if (error) {
        console.error('Error fetching all generator types:', error);
        return [];
    }

    return data || [];
}