import { getSupabaseClient } from "./db";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import type {
  NameGeneration,
  GeneratedName,
  SavedName,
  Generator,
} from "@/types/naming";

/**
 * 保存名字生成记录
 */
export async function saveNameGeneration(data: {
  user_uuid?: string;
  generator_type: string;
  user_prompt: string;
  parameters: any;
  results: GeneratedName[];
}): Promise<NameGeneration> {
  const supabase = getSupabaseClient();
  
  const generation: NameGeneration = {
    uuid: getUuid(),
    user_uuid: data.user_uuid,
    generator_type: data.generator_type,
    user_prompt: data.user_prompt,
    parameters: data.parameters,
    results: data.results,
    created_at: getIsoTimestr(),
  };

  const { data: insertedData, error } = await supabase
    .from("nomenus_name_generations")
    .insert(generation)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to save name generation: ${error.message}`);
  }

  return insertedData;
}

/**
 * 获取用户的名字生成记录
 */
export async function getNameGenerations(
  user_uuid: string,
  generator_type?: string,
  limit: number = 50,
  offset: number = 0
): Promise<NameGeneration[]> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("nomenus_name_generations")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (generator_type) {
    query = query.eq("generator_type", generator_type);
  }

  const { data, error } = await query;

  if (error) {
    throw new Error(`Failed to get name generations: ${error.message}`);
  }

  return data || [];
}

/**
 * 根据UUID获取单个名字生成记录
 */
export async function getNameGenerationByUuid(
  uuid: string
): Promise<NameGeneration | null> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("nomenus_name_generations")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 保存用户收藏的名字
 */
export async function saveUserName(data: {
  user_uuid: string;
  name: string;
  description: string;
  generator_type: string;
  source_generation_uuid?: string;
}): Promise<SavedName> {
  const supabase = getSupabaseClient();

  // 检查是否已经保存过相同的名字
  const { data: existingName } = await supabase
    .from("nomenus_saved_names")
    .select("uuid")
    .eq("user_uuid", data.user_uuid)
    .eq("name", data.name)
    .eq("generator_type", data.generator_type)
    .single();

  if (existingName) {
    throw new Error("This name has already been saved");
  }

  const savedName: SavedName = {
    uuid: getUuid(),
    user_uuid: data.user_uuid,
    name: data.name,
    description: data.description,
    generator_type: data.generator_type,
    source_generation_uuid: data.source_generation_uuid,
    created_at: getIsoTimestr(),
    complexity: 'simple',
    template: 'high_elf',
    userPrompt: '',
  };

  const { data: insertedData, error } = await supabase
    .from("nomenus_saved_names")
    .insert(savedName)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to save name: ${error.message}`);
  }

  return insertedData;
}

/**
 * 获取用户收藏的名字列表
 */
export async function getUserSavedNames(
  user_uuid: string,
  generator_type?: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ names: SavedName[]; total: number }> {
  const supabase = getSupabaseClient();

  let query = supabase
    .from("nomenus_saved_names")
    .select("*", { count: "exact" })
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (generator_type) {
    query = query.eq("generator_type", generator_type);
  }

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to get saved names: ${error.message}`);
  }

  return {
    names: data || [],
    total: count || 0,
  };
}

/**
 * 删除用户收藏的名字
 */
export async function deleteUserSavedName(
  user_uuid: string,
  name_uuid: string
): Promise<void> {
  const supabase = getSupabaseClient();

  const { error } = await supabase
    .from("nomenus_saved_names")
    .delete()
    .eq("user_uuid", user_uuid)
    .eq("uuid", name_uuid);

  if (error) {
    throw new Error(`Failed to delete saved name: ${error.message}`);
  }
}

/**
 * 根据UUID获取保存的名字
 */
export async function getSavedNameByUuid(
  uuid: string
): Promise<SavedName | null> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("nomenus_saved_names")
    .select("*")
    .eq("uuid", uuid)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 获取生成器配置列表
 */
export async function getGenerators(): Promise<Generator[]> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("nomenus_generators")
    .select("*")
    .eq("is_active", true)
    .order("type");

  if (error) {
    throw new Error(`Failed to get generators: ${error.message}`);
  }

  return data || [];
}

/**
 * 根据类型获取生成器配置
 */
export async function getGeneratorByType(
  type: string
): Promise<Generator | null> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("nomenus_generators")
    .select("*")
    .eq("type", type)
    .eq("is_active", true)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 保存或更新生成器配置
 */
export async function saveGenerator(generator: Omit<Generator, 'id' | 'created_at'>): Promise<Generator> {
  const supabase = getSupabaseClient();

  const generatorData = {
    ...generator,
    created_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("nomenus_generators")
    .upsert(generatorData, { onConflict: 'type' })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to save generator: ${error.message}`);
  }

  return data;
}

/**
 * 获取用户的生成统计信息
 */
export async function getUserGenerationStats(
  user_uuid: string
): Promise<{
  total_generations: number;
  total_saved_names: number;
  generations_by_type: Record<string, number>;
}> {
  const supabase = getSupabaseClient();

  // 获取总生成次数
  const { count: totalGenerations } = await supabase
    .from("nomenus_name_generations")
    .select("*", { count: "exact", head: true })
    .eq("user_uuid", user_uuid);

  // 获取总收藏数
  const { count: totalSavedNames } = await supabase
    .from("nomenus_saved_names")
    .select("*", { count: "exact", head: true })
    .eq("user_uuid", user_uuid);

  // 获取按类型分组的生成次数
  const { data: generationsByType } = await supabase
    .from("nomenus_name_generations")
    .select("generator_type")
    .eq("user_uuid", user_uuid);

  const generationStats: Record<string, number> = {};
  generationsByType?.forEach((item) => {
    generationStats[item.generator_type] = 
      (generationStats[item.generator_type] || 0) + 1;
  });

  return {
    total_generations: totalGenerations || 0,
    total_saved_names: totalSavedNames || 0,
    generations_by_type: generationStats,
  };
} 