import { notFound } from 'next/navigation';
import { getGeneratorSeoContent } from '@/services/naming';
import { getAllGeneratorTypes } from '@/services/naming';
import { getLandingPage } from '@/services/page';
import GeneratorPageWrapper from '@/components/generator/GeneratorPageWrapper';

interface GeneratorPageProps {
  id: string;
  locale: string;
}

// 2. Generate unique SEO metadata for each page
export async function generateMetadata({
    params,
  }: {
    params: Promise<{ locale: string, id: string }>;
  }) {
    const { locale, id } = await params;

    try {
      // 从配置中获取生成器信息
      const landingConfig = await getLandingPage(locale);
      const coreConfig = landingConfig.hero?.core_interaction;

      if (coreConfig) {
        const generator = coreConfig.generators.find(g => g.id === id);

        if (generator) {
          let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${id}`;

          if (locale !== "en") {
            canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/${id}`;
          }

          return {
            title: `${generator.name} | AI Fantasy Name Generator`,
            description: generator.description,
            alternates: {
              canonical: canonicalUrl,
            },
          };
        }
      }
    } catch (error) {
      console.error('Failed to load generator metadata:', error);
    }
  }




// 3. The page component itself, fetching data on the server
export default async function GeneratorPage({ params }: { params: Promise<GeneratorPageProps> }) {
  const { id, locale } = await params;

  // 获取SEO内容和核心配置
  const [seoContent, landingConfig] = await Promise.all([
    getGeneratorSeoContent(id, locale),
    getLandingPage(locale)
  ]);

  const coreConfig = landingConfig.hero?.core_interaction;

  if (!coreConfig) {
    throw new Error('Core interaction config not found');
  }

  // 从配置中验证生成器类型是否有效
  const generator = coreConfig.generators.find(g => g.id === id && g.isActive);

  if (!generator) {
    notFound();
  }

  // 使用新的页面组件
  return (
    <GeneratorPageWrapper
      id={id}
      seoContent={seoContent}
      coreConfig={coreConfig}
    />
  );
}