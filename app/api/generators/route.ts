import { NextRequest} from 'next/server';
import { getGenerators, getGeneratorByType } from '@/models/naming';
import { GENERATOR_SUBTYPES } from '@/config/prompts';
import { respData, respErr } from '@/lib/resp';

export async function GET(request: NextRequest) {
  try {
    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type) {
      // 获取特定类型的生成器
      const generator = await getGeneratorByType(type);
      
      if (!generator) {
        return respErr('Generator type not found');
      }

      // 添加子类型选项
      const getSubtypeOptions = (generatorType: string) => {
        switch (generatorType) {
          case 'elf':
            return Object.keys(GENERATOR_SUBTYPES.elf).map(key => ({
              value: key,
              label: GENERATOR_SUBTYPES.elf[key as keyof typeof GENERATOR_SUBTYPES.elf].label,
            }));
          case 'dwarf':
            return Object.keys(GENERATOR_SUBTYPES.dwarf).map(key => ({
              value: key,
              label: GENERATOR_SUBTYPES.dwarf[key as keyof typeof GENERATOR_SUBTYPES.dwarf].label,
            }));
          case 'city':
            return Object.keys(GENERATOR_SUBTYPES.city).map(key => ({
              value: key,
              label: GENERATOR_SUBTYPES.city[key as keyof typeof GENERATOR_SUBTYPES.city].label,
            }));
          default:
            return [];
        }
      };

      const generatorWithSubtypes = {
        ...generator,
        subtypes: getSubtypeOptions(type),
      };

      return respData({
        success: true,
        generator: generatorWithSubtypes,
      });
    } else {
      // 获取所有活跃的生成器
      const generators = await getGenerators();
      
      // 为每个生成器添加子类型选项
      const generatorsWithSubtypes = generators.map(generator => {
        const getSubtypeOptions = (generatorType: string) => {
          switch (generatorType) {
            case 'elf':
              return Object.keys(GENERATOR_SUBTYPES.elf).map(key => ({
                value: key,
                label: GENERATOR_SUBTYPES.elf[key as keyof typeof GENERATOR_SUBTYPES.elf].label,
              }));
            case 'dwarf':
              return Object.keys(GENERATOR_SUBTYPES.dwarf).map(key => ({
                value: key,
                label: GENERATOR_SUBTYPES.dwarf[key as keyof typeof GENERATOR_SUBTYPES.dwarf].label,
              }));
            case 'city':
              return Object.keys(GENERATOR_SUBTYPES.city).map(key => ({
                value: key,
                label: GENERATOR_SUBTYPES.city[key as keyof typeof GENERATOR_SUBTYPES.city].label,
              }));
            default:
              return [];
          }
        };

        return {
          ...generator,
          subtypes: getSubtypeOptions(generator.type),
        };
      });

      return respData({
        success: true,
        generators: generatorsWithSubtypes,
      });
    }

  } catch (error) {
    console.error('Get generators API error:', error);
    
    return respErr('Failed to retrieve generators');
  }
}