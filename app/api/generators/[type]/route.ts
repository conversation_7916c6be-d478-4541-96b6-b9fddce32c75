import { NextResponse } from 'next/server';
import { getSupabaseClient } from '@/models/db';
import { GeneratorConfig } from '@/types/naming';

export const revalidate = 0;

export async function GET(
  request: Request,
  context: { params: Promise<{ type: string }> }
) {
  const { type } = (await context.params);

  if (!type) {
    return NextResponse.json({ error: 'Generator type is required' }, { status: 400 });
  }

  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('nomenus_generators')
      .select('*')
      .eq('type', type)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // PGRST116 is the code for "No rows found" when using .single()
        return NextResponse.json({ error: `Generator with type "${type}" not found.` }, { status: 404 });
      }
      throw error;
    }

    if (!data) {
      return NextResponse.json({ error: `Generator with type "${type}" not found.` }, { status: 404 });
    }

    const generatorConfig: GeneratorConfig = data;

    return NextResponse.json(generatorConfig);

  } catch (error: any) {
    console.error(`Failed to fetch generator config for type: ${type}`, error);
    return NextResponse.json({ error: 'Internal Server Error', details: error.message }, { status: 500 });
  }
} 