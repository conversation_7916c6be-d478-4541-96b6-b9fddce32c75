import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { auth } from '@/auth';
import { saveUserName } from '@/models/naming';
import type { SaveNameRequest } from '@/types/naming';

export async function POST(request: NextRequest) {
  try {
    // 获取用户会话
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return respErr('Authentication required. Please sign in to save names.');
    }

    // 解析请求体
    let body: SaveNameRequest;
    try {
      body = await request.json();
    } catch (error) {
      return respErr('Invalid JSON in request body');
    }

    // 验证必需字段
    if (!body.name || !body.description || !body.generator_type) {
      return respErr('Missing required fields: name, description, and generator_type');
    }

    // 保存名字到数据库
    const savedName = await saveUserName({
      user_uuid: session.user.uuid,
      name: body.name.trim(),
      description: body.description.trim(),
      generator_type: body.generator_type,
      source_generation_uuid: body.source_generation_uuid,
    });

    // 返回成功响应
    return respData({
      success: true,
      saved_name: savedName,
    });

  } catch (error) {
    console.error('Save name API error:', error);
    
    let errorMessage = 'Failed to save name';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      // 处理特定错误
      if (error.message.includes('already been saved')) {
        statusCode = 409; // Conflict
      } else if (error.message.includes('required') || 
                 error.message.includes('Invalid')) {
        statusCode = 400;
      }
    }

    return respErr(errorMessage);
  }
}
