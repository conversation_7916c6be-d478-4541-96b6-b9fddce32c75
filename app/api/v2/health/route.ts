import { NextRequest } from 'next/server';

/**
 * API健康检查端点
 * 用于监控API服务状态
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const timestamp = new Date().toISOString();
    
    // 可以添加更多健康检查逻辑
    // 例如：数据库连接检查、外部服务检查等
    
    return Response.json({
      status: 'healthy',
      timestamp,
      version: 'v2',
      uptime: process.uptime(),
      services: {
        api: 'healthy',
        // database: 'healthy',
        // ai_service: 'healthy'
      }
    });
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return Response.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 503 });
  }
}
