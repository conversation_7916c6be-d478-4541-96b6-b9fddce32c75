import { NextRequest } from 'next/server';
import { getLandingPage } from '@/services/page';

/**
 * 生成器列表API端点
 * 返回可用的生成器配置
 */
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'en'; // 默认使用英文，与项目默认语言一致
    
    console.log(`[API v2] GET /api/v2/generators - locale: ${locale}`);
    
    // 从配置文件获取生成器列表
    const landingConfig = await getLandingPage(locale as 'zh' | 'en');
    const generators = landingConfig.hero?.core_interaction?.generators || [];
    
    // 过滤激活的生成器
    const activeGenerators = generators.filter(g => g.isActive);
    
    // 移除敏感信息（如systemPrompt）
    const publicGenerators = activeGenerators.map(generator => ({
      id: generator.id,
      name: generator.name,
      icon: generator.icon,
      description: generator.description,
      category: generator.category,
      badge: generator.badge,
      placeholder: generator.placeholder,
      order: generator.order
      // 不包含 systemPrompt 和 promptVariables
    }));
    
    return Response.json({
      success: true,
      data: {
        generators: publicGenerators,
        total: publicGenerators.length
      },
      meta: {
        locale,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Get generators API error:', error);
    
    return Response.json({
      success: false,
      error: {
        code: 'FETCH_GENERATORS_FAILED',
        message: error instanceof Error ? error.message : 'Failed to fetch generators'
      }
    }, { status: 500 });
  }
}
