import { NextRequest } from 'next/server';
import { auth } from '@/auth';
import { generateText } from 'ai';
import { getLandingPage } from '@/services/page';
import { getAIModel } from '@/services/naming';
import { saveNameGeneration } from '@/models/naming';
import type {
  CoreInteractionRequest,
  CoreInteractionResponse,
  ApiErrorCode,
  GeneratedResult
} from '@/types/core-interaction-api';
import type { CoreInteractionConfig, GeneratorCard } from '@/types/core-interaction';

export async function POST(request: NextRequest): Promise<Response> {
  const startTime = Date.now();

  try {
    // 获取用户会话
    // const session = await auth();


    // 请求日志
    console.log(`[API v2] POST /api/v2/generate-names - ${new Date().toISOString()}`);

    // 解析请求体
    const body: CoreInteractionRequest = await request.json();

    // 请求参数日志
    console.log('[API v2] Request body:', {
      generator_id: body.generator_id,
      user_prompt: body.user_prompt?.substring(0, 100) + '...',
      settings: body.settings,
    });
    
    // 验证必需字段
    if (!body.generator_id || !body.user_prompt?.trim()) {
      return Response.json({
        success: false,
        error: {
          code: 'INVALID_REQUEST' as ApiErrorCode,
          message: 'Missing required fields: generator_id and user_prompt'
        }
      } as CoreInteractionResponse, { status: 400 });
    }

    // 验证设置参数
    if (!body.settings || typeof body.settings.name_count !== 'number') {
      return Response.json({
        success: false,
        error: {
          code: 'INVALID_REQUEST' as ApiErrorCode,
          message: 'Invalid settings: name_count is required'
        }
      } as CoreInteractionResponse, { status: 400 });
    }

    // 直接在API中实现生成逻辑
    const result = await generateNamesInAPI(body);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`[API v2] Generation completed in ${duration}ms`);

    return Response.json({
      success: true,
      data: {
        generation_id: result.generation_id,
        results: result.results,
      },
      meta: {
        duration_ms: duration,
        timestamp: new Date().toISOString()
      }
    } as CoreInteractionResponse);

  } catch (error) {
    console.error('Generate names V2 API error:', error);
    
    // 根据错误类型返回不同的错误代码
    let errorCode: ApiErrorCode = 'INTERNAL_ERROR' as ApiErrorCode;
    let errorMessage = 'Internal server error';
    
    if (error instanceof Error) {
      if (error.message.includes('not found') || error.message.includes('not configured')) {
        errorCode = 'GENERATOR_NOT_FOUND' as ApiErrorCode;
        errorMessage = error.message;
      } else if (error.message.includes('Failed to generate')) {
        errorCode = 'GENERATION_FAILED' as ApiErrorCode;
        errorMessage = error.message;
      } else {
        errorMessage = error.message;
      }
    }
    
    return Response.json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage
      }
    } as CoreInteractionResponse, { status: 500 });
  }
}

/**
 * 在API中直接实现名字生成逻辑
 * 不依赖外部service，避免循环调用
 */
async function generateNamesInAPI(request: CoreInteractionRequest) {
  try {
    const generator = request.config.generators.find(
      g => g.id === request.generator_id && g.isActive
    );

    if (!generator) {
      throw new Error(`Generator "${request.generator_id}" not found or not active`);
    }

    if (!generator.systemPrompt) {
      throw new Error(`Generator "${request.generator_id}" not configured with system prompt`);
    }

    // 2. 构建 AI prompt
    const systemPrompt = buildSystemPromptInAPI(generator, request.settings, request.config);
    const userPrompt = buildUserPromptInAPI(request.user_prompt, request.settings, request.config);

    console.log('API Internal - System Prompt:', systemPrompt.substring(0, 200) + '...');
    console.log('API Internal - User Prompt:', userPrompt);

    // 3. 调用 AI 生成
    const { text } = await generateText({
      model: getAIModel(),
      system: systemPrompt,
      prompt: userPrompt,
      temperature: 0.8,
      maxTokens: 2000,
    });

    console.log('API Internal - AI Response:', text);

    // 4. 解析结果
    const results = parseAIResponseInAPI(text, request.generator_id);
    console.log('API Internal - Parsed Results:', results);

    // 5. 保存生成记录到数据库
    const generation = await saveNameGeneration({
      user_uuid: undefined, // 暂时不需要用户ID，后续添加认证时可以获取
      generator_type: request.generator_id,
      user_prompt: request.user_prompt,
      parameters: request.settings,
      results: results.map(result => ({
        name: result.name,
        description: result.description,
        pronunciation: result.pronunciation || '',
        tags: result.tags || [request.generator_id],
      }))
    });

    return { generation_id: generation.uuid, results };

  } catch (error) {
    console.error('API Internal - Generation error:', error);
    throw error instanceof Error ? error : new Error('Failed to generate names');
  }
}

/**
 * 构建系统 prompt
 */
function buildSystemPromptInAPI(generator: GeneratorCard, settings: any, config: CoreInteractionConfig): string {
  let prompt = generator.systemPrompt || '';

  // 根据设置调整 prompt
  // if (settings.include_meaning) {
  //   prompt += '\n\n' + config.default_message.include_meaning_prompt;
  // } else {
  //   prompt += '\n\n' + config.default_message.no_include_meaning_prompt;
  // }

  // 根据名字长度调整
  const lengthGuide = {
    'short': config.default_message.name_length_short_prompt,
    'medium': config.default_message.name_length_medium_prompt,
    'long': config.default_message.name_length_long_prompt
  };

  if (settings.name_length && lengthGuide[settings.name_length as keyof typeof lengthGuide]) {
    prompt += `\n\n${lengthGuide[settings.name_length as keyof typeof lengthGuide]}`;
  }

  return prompt;
}

/**
 * 构建用户 prompt
 */
function buildUserPromptInAPI(userPrompt: string, settings: any, config: CoreInteractionConfig): string {
  return `${config.default_message.user_prompt.replace('{name_count}', settings.name_count.toString())}: ${userPrompt}`;
}

/**
 * 解析 AI 响应
 */
function parseAIResponseInAPI(text: string, generatorId: string): GeneratedResult[] {
  try {
    // 清理文本，移除 markdown 代码块标记
    let cleanText = text.trim();

    // 尝试提取JSON部分 - 更智能的方式
    const jsonMatch = cleanText.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      cleanText = jsonMatch[1].trim();
    } else {
      // 如果没有找到 ```json 标记，尝试查找 JSON 数组
      const arrayMatch = cleanText.match(/\[\s*{[\s\S]*}\s*\]/);
      if (arrayMatch) {
        cleanText = arrayMatch[0];
      } else {
        // 移除开头的非JSON文本
        const jsonStart = cleanText.indexOf('[');
        const jsonEnd = cleanText.lastIndexOf(']');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          cleanText = cleanText.substring(jsonStart, jsonEnd + 1);
        } else {
          // 最后尝试：移除 ```json 和 ``` 标记
          if (cleanText.startsWith('```json')) {
            cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
          } else if (cleanText.startsWith('```')) {
            cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
          }
        }
      }
    }

    console.log('API Internal - Cleaned JSON text:', cleanText.substring(0, 200) + '...');

    // 尝试解析 JSON
    const parsed = JSON.parse(cleanText);

    // 如果是数组，直接处理
    if (Array.isArray(parsed)) {
      return parsed.map((item: any, index: number) => ({
        id: `gen-${Date.now()}-${index}`,
        name: item.name || `Generated Name ${index + 1}`,
        pronunciation: item.pronunciation,
        description: item.description || 'A mystical name with ancient origins.',
        tags: item.tags || [generatorId]
      }));
    }

    // 如果有 results 字段
    if (parsed.results && Array.isArray(parsed.results)) {
      return parsed.results.map((item: any, index: number) => ({
        id: `gen-${Date.now()}-${index}`,
        name: item.name || `Generated Name ${index + 1}`,
        pronunciation: item.pronunciation,
        description: item.description || 'A mystical name with ancient origins.',
        tags: item.tags || [generatorId]
      }));
    }

    // 如果是单个对象，包装成数组
    return [{
      id: `gen-${Date.now()}-0`,
      name: parsed.name || 'Generated Name',
      pronunciation: parsed.pronunciation,
      description: parsed.description || 'A mystical name with ancient origins.',
      tags: parsed.tags || [generatorId]
    }];

  } catch (error) {
    console.error('Failed to parse AI response as JSON:', error);
    console.error('Raw text:', text.substring(0, 500) + '...');

    // 尝试从文本中提取名字信息作为降级处理
    const fallbackResults = extractNamesFromText(text, generatorId);
    if (fallbackResults.length > 0) {
      console.log('API Internal - Using fallback extraction, found', fallbackResults.length, 'names');
      return fallbackResults;
    }

    // 最终降级处理：返回默认结果
    console.log('API Internal - Using final fallback result');
    return [{
      id: `gen-${Date.now()}-0`,
      name: 'Mystral',
      pronunciation: 'Mis-trail',
      description: 'An enigmatic name that emerged from the creative process, carrying whispers of ancient magic.',
      tags: [generatorId, 'fallback']
    }];
  }
}

/**
 * 从文本中提取名字信息的降级函数
 */
function extractNamesFromText(text: string, generatorId: string): GeneratedResult[] {
  try {
    const results: GeneratedResult[] = [];

    // 尝试匹配名字模式：引号中的名字
    const nameMatches = text.match(/"([A-Z][a-z]+ [A-Z][a-z]+)"/g);
    if (nameMatches) {
      nameMatches.forEach((match, index) => {
        const name = match.replace(/"/g, '');
        results.push({
          id: `gen-fallback-${Date.now()}-${index}`,
          name: name,
          pronunciation: name.replace(/([A-Z])/g, '$1').trim(),
          description: `A mystical name with ancient origins.`,
          tags: [generatorId, 'extracted']
        });
      });
    }

    return results.slice(0, 5); // 最多返回5个
  } catch (error) {
    console.error('Failed to extract names from text:', error);
    return [];
  }
}
