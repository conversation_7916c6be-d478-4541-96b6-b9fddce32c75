import { NextRequest } from 'next/server';
import { respData, respErr } from '@/lib/resp';
import { auth } from '@/auth';
import { getUserSavedNames, deleteUserSavedName } from '@/models/naming';
import type { GetUserNamesResponse } from '@/types/naming';

export async function GET(request: NextRequest) {
  try {
    // 获取用户会话
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return respErr('Authentication required. Please sign in to view your saved names.');
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const generator_type = searchParams.get('generator_type') || undefined;
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);

    // 获取用户收藏的名字
    const result = await getUserSavedNames(
      session.user.uuid,
      generator_type,
      limit,
      offset
    );

    // 返回成功响应
    return respData({
      success: true,
      names: result.names,
      total: result.total,
    } as GetUserNamesResponse);

  } catch (error) {
    console.error('Get user names API error:', error);
    
    return respErr('Failed to retrieve saved names');
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // 获取用户会话
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return respErr('Authentication required');
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const name_uuid = searchParams.get('uuid');

    if (!name_uuid) {
      return respErr('Missing name UUID');
    }

    // 删除用户收藏的名字
    await deleteUserSavedName(session.user.uuid, name_uuid);

    // 返回成功响应
    return respData({
      success: true,
      message: 'Name deleted successfully',
    });

  } catch (error) {
    console.error('Delete user name API error:', error);
    
    return respErr('Failed to delete saved name');
  }
} 