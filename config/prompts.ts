import type { PromptTemplate } from "@/types/naming";

// Prompt模板配置
export const PROMPT_TEMPLATES: Record<string, PromptTemplate> = {
  // 精灵名字生成器
  elf: {
    generator_type: "elf",
    template_key: "default",
    system_prompt: `You are an expert fantasy name generator specializing in creating authentic, lore-rich elf names. 
Your goal is to generate names that feel both magical and authentic, with deep cultural resonance.

Guidelines:
- Elf names should be melodic and flowing, often featuring vowel combinations
- They should evoke nature, magic, starlight, and ancient wisdom
- Consider different elf subtypes: <PERSON> (elegant, celestial), <PERSON> (natural, earthy), <PERSON> (mysterious, powerful)
- Each name should come with a meaningful background story that explains its origin and significance
- Names should feel timeless and carry weight of ancient heritage

Response format: Return EXACTLY 4 names as a JSON array with this structure:
[
  {
    "name": "Name Here",
    "description": "A rich, story-driven description explaining the name's meaning, origin, and significance",
    "pronunciation": "Optional phonetic guide",
    "meaning": "Core meaning of the name",
    "origin": "Cultural or historical origin"
  }
]`,
    user_prompt_template: `Generate {count} elf names based on this description: "{user_prompt}"

Parameters:
- Gender preference: {gender}
- Name complexity: {complexity}
- Elf subtype: {template}

Make each name unique and ensure the descriptions are rich with lore and meaning.`,
    parameters_mapping: {
      count: "4",
      gender: "any if not specified",
      complexity: "standard if not specified", 
      template: "high_elf if not specified"
    },
    example_output: `[
  {
    "name": "Aelarindel",
    "description": "Born under the convergence of three moons, Aelarindel carries the ancient elven blessing of starlight. This name is given to those destined to bridge the gap between the mortal realm and the celestial planes, often becoming powerful mages or seers.",
    "pronunciation": "AY-lah-RIN-del",
    "meaning": "Starlight Bridge",
    "origin": "High Elven celestial tradition"
  }
]`
  },

  // 矮人名字生成器  
  dwarf: {
    generator_type: "dwarf",
    template_key: "default",
    system_prompt: `You are an expert fantasy name generator specializing in creating authentic dwarf names rooted in tradition and clan heritage.

Guidelines:
- Dwarf names should be strong, consonant-heavy, and reflect their connection to mountains, stone, and craftsmanship
- Many dwarf names reference metals, gems, mountains, or clan achievements
- Consider clan traditions, forge-work, battle honors, and ancestral crafts
- Names often have meanings related to strength, honor, craftsmanship, or geological features
- Each name should reflect the proud, industrious nature of dwarven culture

Response format: Return EXACTLY 4 names as a JSON array with this structure:
[
  {
    "name": "Name Here", 
    "description": "A rich description explaining the name's clan significance, meaning, and cultural importance",
    "pronunciation": "Optional phonetic guide",
    "meaning": "Core meaning of the name",
    "origin": "Clan or cultural origin"
  }
]`,
    user_prompt_template: `Generate {count} dwarf names based on this description: "{user_prompt}"

Parameters:
- Gender preference: {gender}
- Name complexity: {complexity}
- Clan focus: {template}

Ensure each name reflects dwarven values of honor, craft, and clan pride.`,
    parameters_mapping: {
      count: "4",
      gender: "any if not specified",
      complexity: "standard if not specified",
      template: "mountain_clan if not specified"
    },
    example_output: `[
  {
    "name": "Thorek Ironbeard",
    "description": "A name passed down through seven generations of master smiths in the Ironbeard clan. Thorek means 'thunder of the forge' and is given to those who show exceptional skill in metalworking from a young age.",
    "pronunciation": "THOR-ek EYE-run-beard", 
    "meaning": "Thunder of the Forge",
    "origin": "Ironbeard Clan, Master Smith lineage"
  }
]`
  },

  // 奇幻城市名字生成器
  city: {
    generator_type: "city",
    template_key: "default", 
    system_prompt: `You are an expert fantasy world-builder specializing in creating evocative, memorable city and settlement names.

Guidelines:
- City names should evoke the settlement's geography, history, culture, or founding legend
- Consider the city's primary features: port, mountain, forest, desert, etc.
- Names should suggest the city's character: trading hub, fortress, magical academy, etc.
- Include meaningful etymology that reflects the world's linguistic patterns
- Each name should come with rich lore explaining the city's founding, significance, or unique characteristics

Response format: Return EXACTLY 4 names as a JSON array with this structure:
[
  {
    "name": "City Name Here",
    "description": "Rich lore explaining the city's founding, cultural significance, notable features, and place in the world",
    "pronunciation": "Optional phonetic guide", 
    "meaning": "Etymology and meaning",
    "origin": "Founding culture or historical context"
  }
]`,
    user_prompt_template: `Generate {count} fantasy city names based on this description: "{user_prompt}"

Parameters:
- City type: {template}
- Name complexity: {complexity}
- Cultural style: {gender}

Create names that feel lived-in and historically grounded.`,
    parameters_mapping: {
      count: "4",
      template: "trading_port if not specified",
      complexity: "standard if not specified", 
      gender: "mixed_culture if not specified"
    },
    example_output: `[
  {
    "name": "Valdris Harbor",
    "description": "Founded where three rivers converge before flowing into the Sapphire Sea, Valdris Harbor became the greatest trading port of the northern kingdoms. The name derives from 'Val' (convergence) and 'dris' (prosperity), reflecting its position as a crossroads of commerce and culture.",
    "pronunciation": "VAL-dris HAR-bor",
    "meaning": "Convergence of Prosperity",
    "origin": "Ancient Merchant Republic era"
  }
]`
  }
};

// 生成器类型的子模板配置
export const GENERATOR_SUBTYPES = {
  elf: {
    high_elf: {
      label: "高等精灵",
      prompt_modifier: "Focus on celestial, magical, and noble themes. Names should sound refined and otherworldly."
    },
    wood_elf: {
      label: "森林精灵", 
      prompt_modifier: "Emphasize nature, forest, and earth themes. Names should feel connected to the natural world."
    },
    dark_elf: {
      label: "暗夜精灵",
      prompt_modifier: "Create names with mysterious, shadow, and power themes. Should feel dangerous yet elegant."
    }
  },
  dwarf: {
    mountain_clan: {
      label: "山地氏族",
      prompt_modifier: "Focus on stone, mountain, and mining themes. Names should reflect deep mountain traditions."
    },
    forge_master: {
      label: "锻造大师",
      prompt_modifier: "Emphasize metalworking, fire, and craftsmanship. Names should honor the forge traditions."
    },
    warrior_clan: {
      label: "战士氏族", 
      prompt_modifier: "Create names emphasizing battle, honor, and martial prowess."
    }
  },
  city: {
    trading_port: {
      label: "贸易港口",
      prompt_modifier: "Focus on commerce, ships, and cultural exchange. Names should suggest prosperity and connection."
    },
    mountain_fortress: {
      label: "山地要塞",
      prompt_modifier: "Emphasize defense, stone, and strategic position. Names should sound formidable and enduring."
    },
    magical_academy: {
      label: "魔法学院",
      prompt_modifier: "Create names suggesting learning, magic, and ancient knowledge."
    },
    forest_settlement: {
      label: "森林聚落",
      prompt_modifier: "Focus on nature, harmony, and hidden communities within the woods."
    }
  }
};

// 参数复杂度映射
export const COMPLEXITY_MODIFIERS = {
  simple: "Create shorter, simpler names that are easy to pronounce and remember.",
  standard: "Generate names with moderate complexity and length, balancing authenticity with accessibility.", 
  elaborate: "Create longer, more complex names with rich linguistic depth and multiple syllables."
};

// 性别相关的名字修饰
export const GENDER_MODIFIERS = {
  male: "Focus on traditionally masculine name patterns and meanings for this culture.",
  female: "Focus on traditionally feminine name patterns and meanings for this culture.",
  neutral: "Create names that work well regardless of gender, or suggest gender-neutral naming conventions."
};

/**
 * 根据生成器类型和参数构建完整的prompt
 */
export function buildPrompt(
  generator_type: string,
  user_prompt: string,
  parameters: {
    gender?: string;
    complexity?: string;
    template?: string;
    count?: number;
  }
): string {
  const template = PROMPT_TEMPLATES[generator_type];
  if (!template) {
    throw new Error(`Unknown generator type: ${generator_type}`);
  }

  const {
    gender = 'neutral',
    complexity = 'standard',
    template: subtype,
    count = 4
  } = parameters;

  // 获取默认子类型
  const defaultSubtype = (() => {
    switch (generator_type) {
      case 'elf':
        return 'high_elf';
      case 'dwarf':
        return 'mountain_clan';
      case 'city':
        return 'trading_port';
      default:
        return '';
    }
  })();

  const finalSubtype = subtype || defaultSubtype;

  // 构建系统prompt
  let systemPrompt = template.system_prompt;

  // 添加子类型修饰
  const addSubtypeModifier = (type: string, sub: string) => {
    switch (type) {
      case 'elf':
        if (sub === 'high_elf') return GENERATOR_SUBTYPES.elf.high_elf;
        if (sub === 'wood_elf') return GENERATOR_SUBTYPES.elf.wood_elf;
        if (sub === 'dark_elf') return GENERATOR_SUBTYPES.elf.dark_elf;
        break;
      case 'dwarf':
        if (sub === 'mountain_clan') return GENERATOR_SUBTYPES.dwarf.mountain_clan;
        if (sub === 'forge_master') return GENERATOR_SUBTYPES.dwarf.forge_master;
        if (sub === 'warrior_clan') return GENERATOR_SUBTYPES.dwarf.warrior_clan;
        break;
      case 'city':
        if (sub === 'trading_port') return GENERATOR_SUBTYPES.city.trading_port;
        if (sub === 'mountain_fortress') return GENERATOR_SUBTYPES.city.mountain_fortress;
        if (sub === 'magical_academy') return GENERATOR_SUBTYPES.city.magical_academy;
        if (sub === 'forest_settlement') return GENERATOR_SUBTYPES.city.forest_settlement;
        break;
    }
    return null;
  };

  const subtypeConfig = addSubtypeModifier(generator_type, finalSubtype);
  if (subtypeConfig) {
    systemPrompt += `\n\nSpecial focus for ${subtypeConfig.label}: ${subtypeConfig.prompt_modifier}`;
  }

  // 添加复杂度修饰
  if (COMPLEXITY_MODIFIERS[complexity as keyof typeof COMPLEXITY_MODIFIERS]) {
    systemPrompt += `\n\nComplexity guideline: ${COMPLEXITY_MODIFIERS[complexity as keyof typeof COMPLEXITY_MODIFIERS]}`;
  }

  // 添加性别修饰
  if (gender !== 'neutral' && GENDER_MODIFIERS[gender as keyof typeof GENDER_MODIFIERS]) {
    systemPrompt += `\n\nGender consideration: ${GENDER_MODIFIERS[gender as keyof typeof GENDER_MODIFIERS]}`;
  }

  // 构建用户prompt
  let userPrompt = template.user_prompt_template
    .replace('{count}', count.toString())
    .replace('{user_prompt}', user_prompt)
    .replace('{gender}', gender)
    .replace('{complexity}', complexity)
    .replace('{template}', finalSubtype);

  return `${systemPrompt}\n\n${userPrompt}`;
} 