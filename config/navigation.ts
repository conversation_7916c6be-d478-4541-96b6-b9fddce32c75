import { NavigationConfig } from "@/types/navigation";
import { getLandingPage } from "@/services/page";

/**
 * 获取导航配置
 */
export async function getNavigationConfig(locale: string): Promise<NavigationConfig> {
  try {
    const landingConfig = await getLandingPage(locale);
    const coreConfig = landingConfig.hero?.core_interaction;
    
    if (!coreConfig) {
      return { categories: [], generators: [] };
    }

    // 过滤并转换生成器数据
    const generators = coreConfig.generators
      .filter(g => g.isActive && g.id !== 'custom') // 排除自定义生成器
      .map(g => ({
        id: g.id,
        name: g.name,
        icon: g.icon,
        description: g.description,
        category: g.category,
        badge: g.badge,
        url: `/${g.id}`,
        order: g.order,
        isActive: g.isActive
      }));

    // 过滤有生成器的分类
    const usedCategories = new Set(generators.map(g => g.category));
    const categories = coreConfig.categories
      .filter(cat => cat.id !== 'all' && usedCategories.has(cat.id))
      .map(cat => ({
        id: cat.id,
        name: cat.name,
        icon: cat.icon,
        order: cat.order
      }));

    return {
      categories: categories.sort((a, b) => a.order - b.order),
      generators: generators.sort((a, b) => a.order - b.order)
    };
  } catch (error) {
    console.error('Failed to load navigation config:', error);
    return { categories: [], generators: [] };
  }
}
