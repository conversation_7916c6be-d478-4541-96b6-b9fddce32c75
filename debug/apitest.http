@baseUrl = http://localhost:3000
@apiKey = sk-0mvdibrsr3hQHPxDi1pDyeztSzGg71dgoLgvSEZOpxdnM7qJ

### ping api
POST {{baseUrl}}/api/ping
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "message": "hello"
}

### get user info
POST {{baseUrl}}/api/get-user-info
Content-Type: application/json
Authorization: Bearer {{apiKey}}


### get user credits
POST {{baseUrl}}/api/get-user-credits
Content-Type: application/json
Authorization: Bearer {{apiKey}}




### gen image
POST {{baseUrl}}/api/demo/gen-image
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "a beautiful girl running with 2 cats",
  "provider": "replicate",
  "model": "black-forest-labs/flux-schnell"
}

### gen text 
POST {{baseUrl}}/api/demo/gen-text
Content-Type: application/json
Authorization: Bearer {{api<PERSON><PERSON>}}

{
  "prompt": "9.11 vs 9.8, which one is greater?",
  "provider": "openrouter",
  "model": "deepseek/deepseek-r1"
}

### generate elf names - 精灵名字生成（匿名用户）
POST {{baseUrl}}/api/generate-names
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "generator_type": "elf",
  "user_prompt": "一个勇敢的精灵战士，擅长使用弓箭，热爱自然",
  "parameters": {
    "gender": "male",
    "complexity": "standard",
    "template": "high_elf",
    "count": 4
  }
}

### generate elf names - 精灵名字生成（已登录用户）
POST {{baseUrl}}/api/generate-names
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "generator_type": "elf",
  "user_prompt": "一个神秘的精灵法师，掌握古老的魔法知识",
  "parameters": {
    "gender": "female",
    "complexity": "elaborate",
    "template": "wood_elf",
    "count": 4
  }
}

### generate dwarf names - 矮人名字生成
POST {{baseUrl}}/api/generate-names
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "generator_type": "dwarf",
  "user_prompt": "一个技艺精湛的铁匠，来自古老的山地氏族",
  "parameters": {
    "gender": "male",
    "complexity": "standard",
    "template": "forge_master",
    "count": 4
  }
}

### generate city names - 奇幻城市名字生成
POST {{baseUrl}}/api/generate-names
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "generator_type": "city",
  "user_prompt": "一个繁华的海港城市，是多种族贸易的中心",
  "parameters": {
    "complexity": "standard",
    "template": "trading_port",
    "gender": "mixed_culture",
    "count": 4
  }
}

### save name - 保存名字（需要登录）
POST {{baseUrl}}/api/save-name
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "name": "Aelarindel",
  "description": "Born under the convergence of three moons, Aelarindel carries the ancient elven blessing of starlight.",
  "generator_type": "elf",
  "source_generation_uuid": "optional-generation-uuid"
}

### get user saved names - 获取用户收藏的名字
GET {{baseUrl}}/api/user-names?generator_type=elf&limit=10&offset=0
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### get all user saved names - 获取所有收藏的名字
GET {{baseUrl}}/api/user-names
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### delete saved name - 删除收藏的名字
DELETE {{baseUrl}}/api/user-names?uuid=your-saved-name-uuid
Content-Type: application/json
Authorization: Bearer {{apiKey}}

### get generators config - 获取生成器配置
GET {{baseUrl}}/api/generators
Content-Type: application/json

### generate names v2 - 名字生成 v2
POST {{baseUrl}}/api/v2/generate-names
Content-Type: application/json

{
    "generator_id": "elf",
    "user_prompt": "一个高贵的高等精灵法师",
    "settings": {
      "name_count": 5,
      "include_meaning": true,
      "name_length": "medium"
    }
}