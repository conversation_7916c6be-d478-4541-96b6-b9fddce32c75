# 导航栏超级菜单实现方案

## 1. 需求分析

### 1.1 功能需求
基于 `docs/0714-导航栏.md` 的产品需求，需要实现：

- **PC端**：鼠标悬停触发超级菜单展开
- **移动端**：点击触发全屏/抽屉式菜单
- **布局**：多列布局（PC端2-3列），分类标题作为视觉分隔符
- **内容**：每个生成器显示图标、名称、描述、可选角标
- **动画**：平滑的展开/收起过渡效果（0.2-0.3秒）
- **SEO**：所有链接渲染为标准 `<a>` 标签

### 1.2 现状分析
当前系统已有：
- NavigationMenu 组件（基于 Radix UI）
- Sheet 组件用于移动端菜单
- 生成器配置在 `i18n/pages/landing/zh.json`
- 7个活跃生成器，5个分类
- 多语言支持（中英文）

## 2. 技术架构设计

### 2.1 组件架构
```
Header (现有)
├── NavigationMenu (现有)
│   ├── NavigationMenuItem (现有)
│   └── GeneratorMegaMenu (新增)
│       ├── MegaMenuContent
│       ├── CategorySection
│       └── GeneratorNavItem
└── MobileMenu (增强)
    ├── Sheet (现有)
    └── MobileGeneratorMenu (新增)
        ├── Accordion (现有)
        └── GeneratorNavItem (复用)
```

### 2.2 数据流设计
```
getLandingPage(locale) 
→ hero.core_interaction.generators 
→ NavigationConfig 
→ MegaMenu/MobileMenu
```

## 3. 数据结构设计

### 3.1 导航配置类型定义
```typescript
// types/navigation.d.ts
export interface NavigationCategory {
  id: string;
  name: string;
  icon: string;
  order: number;
}

export interface NavigationGenerator {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
  badge?: 'hot' | 'new' | 'popular';
  url: string;
  order: number;
  isActive: boolean;
}

export interface NavigationConfig {
  categories: NavigationCategory[];
  generators: NavigationGenerator[];
}
```

### 3.2 配置文件结构
```typescript
// config/navigation.ts
export async function getNavigationConfig(locale: string): Promise<NavigationConfig> {
  const landingConfig = await getLandingPage(locale);
  const coreConfig = landingConfig.hero?.core_interaction;
  
  return {
    categories: coreConfig?.categories || [],
    generators: coreConfig?.generators
      .filter(g => g.isActive)
      .map(g => ({
        ...g,
        url: `/${g.id}`
      })) || []
  };
}
```

## 4. 组件实现规范

### 4.1 MegaMenu 组件
```typescript
// components/navigation/MegaMenu.tsx
interface MegaMenuProps {
  config: NavigationConfig;
  locale: string;
}

export default function MegaMenu({ config, locale }: MegaMenuProps) {
  // 按分类组织生成器
  const generatorsByCategory = groupBy(config.generators, 'category');
  
  return (
    <NavigationMenuContent className="w-[800px] p-6">
      <div className="grid grid-cols-3 gap-6">
        {config.categories
          .filter(cat => cat.id !== 'all')
          .map(category => (
            <CategorySection 
              key={category.id}
              category={category}
              generators={generatorsByCategory[category.id] || []}
            />
          ))}
      </div>
    </NavigationMenuContent>
  );
}
```

### 4.2 CategorySection 组件
```typescript
// components/navigation/CategorySection.tsx
interface CategorySectionProps {
  category: NavigationCategory;
  generators: NavigationGenerator[];
}

export default function CategorySection({ category, generators }: CategorySectionProps) {
  return (
    <div className="space-y-3">
      {/* 分类标题 */}
      <div className="flex items-center gap-2 pb-2 border-b border-border">
        <Icon name={category.icon} className="w-4 h-4 text-primary" />
        <h3 className="font-semibold text-sm text-foreground">{category.name}</h3>
      </div>
      
      {/* 生成器列表 */}
      <div className="space-y-1">
        {generators.map(generator => (
          <GeneratorNavItem key={generator.id} generator={generator} />
        ))}
      </div>
    </div>
  );
}
```

### 4.3 GeneratorNavItem 组件
```typescript
// components/navigation/GeneratorNavItem.tsx
interface GeneratorNavItemProps {
  generator: NavigationGenerator;
  variant?: 'mega' | 'mobile';
}

export default function GeneratorNavItem({ generator, variant = 'mega' }: GeneratorNavItemProps) {
  return (
    <Link
      href={generator.url as any}
      className={cn(
        "group flex items-start gap-3 p-3 rounded-lg transition-colors",
        "hover:bg-accent hover:text-accent-foreground",
        variant === 'mobile' && "border-b border-border last:border-0"
      )}
    >
      {/* 图标 */}
      <Icon 
        name={generator.icon} 
        className="w-5 h-5 text-primary shrink-0 mt-0.5" 
      />
      
      <div className="flex-1 min-w-0">
        {/* 名称和角标 */}
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium text-sm text-foreground group-hover:text-accent-foreground">
            {generator.name}
          </h4>
          {generator.badge && (
            <Badge variant={getBadgeVariant(generator.badge)} className="text-xs">
              {getBadgeText(generator.badge)}
            </Badge>
          )}
        </div>
        
        {/* 描述 */}
        <p className="text-xs text-muted-foreground line-clamp-2">
          {generator.description}
        </p>
      </div>
    </Link>
  );
}
```

## 5. 集成方案

### 5.1 Header 组件修改
在 `components/blocks/header/index.tsx` 中添加生成器菜单项：

```typescript
// 在现有导航项中添加
{
  title: "生成器",
  icon: "wand-magic-sparkles",
  children: [] // 触发 MegaMenu 渲染
}
```

### 5.2 移动端集成
增强现有的 Sheet 菜单，添加生成器手风琴：

```typescript
// 在移动端菜单中添加
<AccordionItem value="generators">
  <AccordionTrigger>
    <Icon name="wand-magic-sparkles" className="w-4 h-4 mr-2" />
    生成器
  </AccordionTrigger>
  <AccordionContent>
    <MobileGeneratorMenu config={navigationConfig} />
  </AccordionContent>
</AccordionItem>
```

## 6. 样式和动画规范

### 6.1 布局规范
- **PC端宽度**：800px
- **列数**：3列（每个分类一列）
- **间距**：gap-6（24px）
- **内边距**：p-6（24px）

### 6.2 动画规范
- **展开动画**：fade-in + slide-down，duration-200
- **悬停效果**：background-color transition，duration-150
- **移动端**：slide-in-from-right，duration-300

### 6.3 响应式设计
```css
/* PC端 */
@media (min-width: 1024px) {
  .mega-menu {
    width: 800px;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 平板端 */
@media (min-width: 768px) and (max-width: 1023px) {
  .mega-menu {
    width: 600px;
    grid-template-columns: repeat(2, 1fr);
  }
}
```

## 7. SEO 优化

### 7.1 链接结构
- 所有生成器链接使用 Next.js `<Link>` 组件
- 自动渲染为 `<a href="...">` 标签
- 支持多语言路径：`/zh/elf-name-generator`

### 7.2 结构化数据
```html
<!-- 在 head 中添加导航结构化数据 -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "SiteNavigationElement",
  "name": "AI生成器",
  "url": "/generators"
}
</script>
```

## 8. 性能优化

### 8.1 代码分割
- MegaMenu 组件使用动态导入
- 移动端和PC端组件分别打包

### 8.2 数据缓存
- 导航配置数据在构建时静态生成
- 使用 SWR 或 React Query 缓存客户端数据

## 9. 测试策略

### 9.1 单元测试
- 组件渲染测试
- 数据转换函数测试
- 交互行为测试

### 9.2 集成测试
- 导航菜单展开/收起
- 移动端响应式测试
- 多语言切换测试

### 9.3 E2E 测试
- 完整用户流程测试
- SEO 爬虫可访问性测试
- 性能基准测试

## 10. 部署和监控

### 10.1 部署检查清单
- [ ] 所有链接正确跳转
- [ ] 移动端菜单正常工作
- [ ] 多语言配置正确
- [ ] SEO 标签完整
- [ ] 性能指标达标

### 10.2 监控指标
- 导航菜单使用率
- 生成器页面访问量
- 移动端交互率
- 页面加载性能
