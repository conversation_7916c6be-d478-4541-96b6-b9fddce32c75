# API 安全防护方案

本文档描述了在不增加用户登录验证的情况下，如何保护 API 免受滥用的安全措施。

## 🛡️ 安全措施概览

### 1. 请求频率限制（Rate Limiting）
- **服务端限流**：每分钟最多 5 次请求
- **客户端限流**：前端也会进行频率检查
- **动态调整**：可根据需要调整限流参数

### 2. 请求来源验证
- **Origin 检查**：验证请求来源域名
- **Referer 检查**：验证页面来源
- **User-Agent 检查**：基本的机器人检测

### 3. 请求签名机制
- **时间戳验证**：防止重放攻击
- **Nonce 验证**：防止请求重复
- **签名验证**：确保请求完整性

### 4. 客户端防护
- **开发者工具检测**：检测并警告开发者工具使用
- **请求混淆**：对 API 调用进行基本混淆
- **客户端限流**：前端预先检查请求频率

## 📝 配置说明

### 环境变量
```bash
# API 安全密钥
API_SECRET=your-api-secret-key-here
```

### 限流配置
```typescript
// 在 lib/rate-limit.ts 中配置
export const rateLimitConfigs = {
  generateNames: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 5       // 最多5次请求
  }
};
```

## 🔧 使用方法

### 服务端 API 路由
```typescript
import { createRateLimiter, rateLimitConfigs } from '@/lib/rate-limit';
import { validateRequest } from '@/lib/request-validation';

const rateLimiter = createRateLimiter(rateLimitConfigs.generateNames);

export async function POST(request: NextRequest) {
  // 1. 限流检查
  const rateLimitResponse = rateLimiter(request);
  if (rateLimitResponse) {
    return rateLimitResponse;
  }

  // 2. 请求验证
  const bodyText = await request.text();
  const validation = validateRequest(request, bodyText);
  if (!validation.valid) {
    return new Response('Request validation failed', { status: 403 });
  }

  // 3. 处理请求...
}
```

### 客户端安全请求
```typescript
import { createSecureRequest, obfuscateApiCall, clientRateLimit } from '@/lib/client-security';

// 使用安全请求
const response = await createSecureRequest('/api/v2/generate-names', {
  method: 'POST',
  body: JSON.stringify(data)
});

// 使用混淆包装
const secureApiCall = obfuscateApiCall(async () => {
  // 检查客户端限流
  if (!clientRateLimit.canMakeRequest()) {
    throw new Error('请求过于频繁');
  }
  
  // 执行 API 调用
  return await createSecureRequest('/api/endpoint', options);
});
```

## ⚠️ 安全限制说明

### 这些措施能防护什么：
- ✅ 普通用户的意外过度使用
- ✅ 简单的自动化脚本攻击
- ✅ 基本的爬虫和机器人
- ✅ 开发者工具中的简单重复调用

### 这些措施无法完全防护：
- ❌ 专业的逆向工程攻击
- ❌ 分布式攻击（多IP）
- ❌ 高级的自动化工具
- ❌ 有经验的恶意用户

## 🚀 进一步增强建议

如果需要更强的安全防护，建议考虑：

1. **添加用户认证**：最有效的防护方式
2. **使用 CDN 防护**：如 Cloudflare 的 DDoS 防护
3. **IP 白名单**：限制特定 IP 访问
4. **验证码系统**：在频繁请求时要求验证码
5. **API 网关**：使用专业的 API 管理服务

## 📊 监控和日志

系统会记录以下信息：
- 请求频率超限的 IP
- 验证失败的请求
- 异常的请求模式

建议定期检查日志，识别潜在的滥用行为。

## 🔄 维护建议

1. **定期更新密钥**：更换 API_SECRET
2. **调整限流参数**：根据实际使用情况优化
3. **监控性能影响**：确保安全措施不影响正常用户体验
4. **更新检测规则**：根据新的攻击模式调整防护策略
