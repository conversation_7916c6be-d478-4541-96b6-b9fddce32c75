# 生成器内页增加流程

本文档详细说明如何为 Nomenus 项目添加新的生成器内页。系统采用配置驱动的方式，让添加新页面变得简单高效。

## 🏗️ 内页结构概览

每个生成器内页包含以下核心区域：

1. **页面标题区域** - 生成器名称和描述
2. **核心交互区域** - 输入框、设置选项、生成按钮和结果展示
3. **使用指南区域** - 3步骤交互式指南（可选）
4. **SEO内容区域** - 包含5个子区域：
   - 灵感画廊 (Inspiration Gallery)
   - 优势说明 (Why Us)
   - 提示指南 (Prompt Guide)
   - 风格探索 (Style Exploration)
   - 常见问题 (FAQ)

## 📋 完整操作步骤

### 步骤1：在主配置中添加生成器定义

**需要修改的文件：**
- `i18n/pages/landing/en.json`
- `i18n/pages/landing/zh.json`

**操作位置：** 在 `hero.core_interaction.generators` 数组中添加新的生成器配置

**配置模板：**
```json
{
  "id": "new-generator-id",
  "name": "New Generator Name",
  "icon": "icon-name",
  "description": "Generator description",
  "category": "fantasy|sci-fi|modern|popular",
  "badge": "hot|new|popular",
  "isActive": true,
  "systemPrompt": "You are a professional creative name generator. Based on the user's description, generate creative and meaningful names. Please ensure the names match the user's requirements and background settings.\n\nReturn format: Return a JSON array containing the specified number of name objects:\n[\n  {\n    \"name\": \"Name\",\n    \"description\": \"Detailed meaning and background description\",\n    \"pronunciation\": \"Pronunciation guide (optional)\",\n    \"tags\": [\"tag1\", \"tag2\"]\n  }\n]",
  "promptVariables": ["user_prompt", "name_count", "include_meaning", "name_length"]
}
```

**字段说明：**
- `id`: 生成器唯一标识符，用于URL路径
- `name`: 显示名称
- `icon`: 图标名称（参考现有图标）
- `description`: 生成器描述
- `category`: 分类（fantasy/sci-fi/modern/popular）
- `badge`: 角标（可选：hot/new/popular）
- `isActive`: 是否启用（必须为true）
- `systemPrompt`: AI系统提示词
- `promptVariables`: 提示词变量列表

### 步骤2：创建SEO内容文件

**文件位置：** `i18n/pages/generators/`

**需要创建的文件：**
- `{generator-id}.en.json` （英文SEO内容）
- `{generator-id}.zh.json` （中文SEO内容）

**完整文件结构模板：**
```json
{
  "seo_content": {
    "how_to_use": {
      "title": "How to Use This [Generator Name]",
      "intro": "Follow these simple steps to create the perfect [type] name for your character",
      "step1": {
        "title": "Describe Your Character",
        "description": "Enter detailed information about your character's background, personality, and role",
        "tip1": "Include specific profession or role",
        "tip2": "Describe personality traits and characteristics",
        "tip3": "Mention cultural background or setting"
      },
      "step2": {
        "title": "Customize Settings",
        "description": "Choose your preferences for name quantity, length, and additional features",
        "tip1": "Generate 5-10 names for the best variety",
        "tip2": "Select 'Medium' length for most authentic results",
        "tip3": "Enable backstory to get detailed cultural meanings and character lore"
      },
      "step3": {
        "title": "Generate & Save",
        "description": "Create your names and save the ones that resonate with your character",
        "tip1": "Try different descriptions for varied results",
        "tip2": "Use the favorite feature to build your character collection",
        "tip3": "Regenerate if you want more options"
      },
      "tips_title": "Pro Tips",
      "previous": "Previous",
      "next": "Next"
    },
    "inspiration_gallery": {
      "title": "Featured [Type] Name Creations",
      "description": "Get inspired by these AI-generated names with their backstories and meanings.",
      "items": [
        {
          "name": "Example Name",
          "story": "Background story and meaning explanation",
          "pronunciation": "Pronunciation guide"
        },
        {
          "name": "Another Example",
          "story": "Another background story",
          "pronunciation": "Another pronunciation guide"
        }
      ]
    },
    "why_us": {
      "title": "Why Our [Type] Name Creator Stands Apart",
      "content": "Detailed explanation of what makes this generator special and unique."
    },
    "prompt_guide": {
      "title": "Mastering the Art of Effective Prompts",
      "intro": "Learn how to craft better prompts for optimal results:",
      "tips": [
        {
          "title": "Be Specific & Evocative",
          "description": "Provide detailed descriptions instead of generic terms."
        },
        {
          "title": "Include Personality & Background",
          "description": "Add character traits and background information."
        },
        {
          "title": "Describe Their World",
          "description": "Mention environment and cultural context."
        },
        {
          "title": "Consider Their Role",
          "description": "Specify profession or calling for better results."
        }
      ]
    },
    "style_exploration": {
      "title": "Exploring Diverse [Type] Naming Traditions",
      "intro": "Different styles and approaches to [type] names:",
      "items": [
        {
          "style_title": "Style 1: Traditional Approach",
          "style_description": "Description of this naming style and its characteristics.",
          "prompt_example": "Try prompting with: 'Example prompt for this style.'"
        },
        {
          "style_title": "Style 2: Modern Approach",
          "style_description": "Description of this naming style and its characteristics.",
          "prompt_example": "For this style, try: 'Example prompt for modern approach.'"
        }
      ]
    },
    "faq": {
      "title": "Frequently Asked Questions",
      "categories": [
        {
          "category": "Basic Usage",
          "items": [
            {
              "question": "How do I generate better names?",
              "answer": "Detailed answer explaining best practices."
            },
            {
              "question": "Can I specify gender or other attributes?",
              "answer": "Explanation of customization options available."
            }
          ]
        },
        {
          "category": "Name Characteristics",
          "items": [
            {
              "question": "What makes these names unique?",
              "answer": "Explanation of the generator's approach and methodology."
            }
          ]
        },
        {
          "category": "Usage Rights",
          "items": [
            {
              "question": "Can I use generated names commercially?",
              "answer": "Yes! All names are free to use in any project."
            }
          ]
        }
      ],
      "related_generators": {
        "title": "Explore More Fantasy Name Generators",
        "items": [
          {
            "title": "Elf Name Generator",
            "description": "Create elegant and mystical names for elven characters with rich cultural backgrounds.",
            "url": "/elf-name-generator",
            "icon": "RiLeafLine"
          },
          {
            "title": "Dwarf Name Generator",
            "description": "Forge powerful, clan-honored names from mountain halls and ancient traditions.",
            "url": "/dwarf-name-generator",
            "icon": "RiHammerLine"
          },
          {
            "title": "D&D Name Generator",
            "description": "Generate perfect names for any D&D character with backstories and cultural context.",
            "url": "/dnd-name-generator",
            "icon": "RiSwordLine"
          }
        ]
      }
    }
  }
}
```

### 步骤3：验证配置

**检查清单：**
- ✅ 生成器ID在英文和中文配置文件中完全一致
- ✅ `isActive: true` 确保生成器被启用
- ✅ SEO文件命名格式正确：`{id}.{locale}.json`
- ✅ 分类（category）是有效的分类名称
- ✅ 图标名称存在于系统中
- ✅ systemPrompt 格式正确，包含必要的JSON返回格式说明

### 步骤4：测试新页面

**自动生成的访问路径：**
- 英文页面：`/en/{generator-id}`
- 中文页面：`/zh/{generator-id}`
- 默认页面：`/{generator-id}`

**测试项目：**
- ✅ 页面能正常访问
- ✅ SEO元数据正确显示
- ✅ 生成器在导航菜单中显示
- ✅ 生成器在选择器中显示
- ✅ 核心交互功能正常工作
- ✅ 多语言切换正常

## 🔄 系统自动处理的功能

添加配置后，系统会自动处理：

1. **路由生成** - 动态路由自动识别新的生成器ID
2. **SEO元数据** - 自动生成title、description、canonical URL
3. **导航显示** - 自动在导航菜单中显示新生成器
4. **选择器显示** - 自动在生成器选择器中显示
5. **多语言支持** - 自动处理中英文切换
6. **404处理** - 配置错误时自动显示404页面
7. **分类归属** - 自动按category分类显示
8. **组件渲染** - 自动渲染所有SEO内容区域
9. **响应式布局** - 自动适配移动端和桌面端
10. **主题支持** - 自动支持深色/浅色模式切换

## 📱 内页组件架构

### 核心组件结构
```
GeneratorPageWrapper
├── 页面标题区域 (Page Header)
├── 核心交互区域 (CoreInteractionZone)
├── 使用指南区域 (HowToUseGuide) [可选]
└── SEO内容区域 (GeneratorSeoContent)
    ├── InspirationGallery
    ├── WhyUs
    ├── PromptGuide
    ├── StyleExploration
    └── FaqSection
```

### 组件功能说明

**GeneratorPageWrapper** - 主容器组件
- 统一页面布局和样式
- 处理数据传递和状态管理
- 集成API调用逻辑

**CoreInteractionZone** - 核心交互功能
- 输入框和设置选项
- 生成按钮和加载状态
- 结果展示和操作功能

**HowToUseGuide** - 使用指南（可选）
- 3步骤交互式指南
- 步骤导航和内容切换
- 专业提示和最佳实践

**GeneratorSeoContent** - SEO内容区域
- 5个子区域的统一管理
- 响应式布局和样式统一
- 数据驱动的内容渲染

## ⚠️ 注意事项

1. **ID命名规范**：使用小写字母和连字符，如 `dragon-name-generator`
2. **文件命名**：SEO文件必须严格按照 `{id}.{locale}.json` 格式命名
3. **配置一致性**：确保英文和中文配置中的ID、category等关键字段一致
4. **图标可用性**：使用的图标名称必须在系统中存在
5. **分类有效性**：category必须是系统支持的分类（fantasy/sci-fi/modern/popular）

## 🚀 快速开始示例

假设要添加一个"Dragon Name Generator"：

1. **配置ID**: `dragon-name-generator`
2. **修改配置文件**：在两个landing配置文件中添加生成器定义
3. **创建SEO文件**：
   - `dragon-name-generator.en.json`
   - `dragon-name-generator.zh.json`
4. **访问测试**：`/en/dragon-name-generator`

完成这些步骤后，新的生成器页面就会自动可用，包含完整的功能和SEO优化！

## 🔧 现有内页迭代升级

### 当前已有的生成器内页
- `elf-name-generator` - 精灵名字生成器
- `dwarf-name-generator` - 矮人名字生成器
- `dnd-name-generator` - D&D名字生成器

### 升级检查清单

对于现有内页，需要确保包含以下完整结构：

**✅ 必需组件：**
- [x] 页面标题区域
- [x] 核心交互区域
- [x] SEO内容区域（5个子区域）

**🔄 可选组件：**
- [x] 使用指南区域（how_to_use）- **已为所有现有生成器添加**

**📋 SEO内容完整性检查：**
- [x] inspiration_gallery - 灵感画廊
- [x] why_us - 优势说明
- [x] prompt_guide - 提示指南
- [x] style_exploration - 风格探索
- [x] faq - 常见问题（**必须包含categories结构和related_generators**）

**🎯 FAQ区域标准结构：**
```json
"faq": {
  "title": "Frequently Asked Questions",
  "categories": [
    {
      "category": "Basic Usage",
      "items": [...]
    },
    {
      "category": "Name Characteristics",
      "items": [...]
    },
    {
      "category": "Usage Rights",
      "items": [...]
    }
  ],
  "related_generators": {
    "title": "Explore More Fantasy Name Generators",
    "items": [...]
  }
}
```

### 升级步骤

1. **检查现有SEO文件**：确保包含所有必需的区域
2. **补充缺失内容**：添加缺失的区域或优化现有内容
3. **统一FAQ结构**：确保所有生成器的FAQ都使用categories结构
4. **添加使用指南**：为缺少how_to_use的生成器添加3步骤指南
5. **统一样式风格**：确保与最新的设计规范一致
6. **测试功能完整性**：验证所有交互功能正常工作
7. **优化用户体验**：根据用户反馈持续改进

### ✅ 已完成的升级工作

**现有生成器升级状态：**
- **elf-name-generator** ✅ 完整（标准模板）
- **dwarf-name-generator** ✅ 已升级
  - ✅ 添加了how_to_use区域
  - ✅ 修复了FAQ的categories结构
  - ✅ 添加了related_generators
- **dnd-name-generator** ✅ 已升级
  - ✅ 添加了how_to_use区域
  - ✅ 修复了FAQ的categories结构
  - ✅ 优化了related_generators

**升级内容包括：**
1. **使用指南区域**：为所有生成器添加了统一的3步骤交互式指南
2. **FAQ结构标准化**：统一使用categories分类结构
3. **相关生成器推荐**：在FAQ底部添加related_generators推荐
4. **多语言一致性**：确保中英文版本结构完全一致

## 📝 维护建议

### 内容维护
- 定期更新inspiration_gallery中的示例名字
- 根据用户反馈优化prompt_guide的建议
- 扩展style_exploration以覆盖更多命名风格
- 更新FAQ以回答用户常见问题

### 技术维护
- 定期检查生成器的活跃度，调整 `isActive` 状态
- 根据用户反馈优化 `systemPrompt` 提示词
- 监控API性能和响应时间
- 更新SEO内容以提高搜索排名

### 数据分析
- 监控新页面的访问数据和用户体验
- 分析用户最常使用的功能和设置
- 收集用户对生成结果的满意度反馈
- 基于数据优化生成器配置和内容