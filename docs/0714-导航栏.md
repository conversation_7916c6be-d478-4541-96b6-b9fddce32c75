### **Part 1: 产品需求文档 (PRD) - 超级导航菜单功能**

**1. 功能名称:**
*   智能导航超级菜单 (Smart Navigation Mega Menu)

**2. 背景与目标:**
*   **背景:** 当前网站需要一个统一的导航系统，以帮助用户发现我们所有的AI命名生成器，并为搜索引擎提供清晰的内链结构。简单的下拉列表信息量不足，无法体现产品的吸引力。
*   **目标:**
    *   **提升用户发现性:** 通过丰富的视觉元素和信息，让用户能快速理解并探索我们提供的所有工具。
    *   **增强SEO内链:** 构建一个清晰、完整的网站链接结构，帮助搜索引擎爬虫索引所有精品工具页。
    *   **提高用户点击率:** 用更具吸引力的方式展示每个生成器，鼓励用户尝试更多功能。

**3. 用户故事:**
*   **作为一个新用户，**我希望能够一目了然地看到网站都提供了哪些类型的生成器，并通过图标和简短描述快速判断哪个是我需要的，从而减少我的探索时间。
*   **作为一个老用户，**我希望能从一个固定的入口快速跳转到我常用的工具，而不是每次都在首页输入。
*   **作为一个搜索引擎爬虫，**我希望能在一个集中的地方找到通往网站所有重要页面的链接，以便我能完整地索引整个网站的结构。

**4. 功能需求详述 (Requirements):**

*   **4.1 触发方式:**
    *   在PC端，当用户的鼠标光标**悬停 (Hover)** 在顶部导航栏的“生成器 (Generators)”链接上时，超级菜单应平滑地向下展开。
    *   在移动端，当用户**点击 (Tap)** 汉堡菜单中的“生成器”链接时，超级菜单应以全屏或抽屉的形式展现。

*   **4.2 菜单布局与结构:**
    *   菜单应为一个**宽度较大**的面板，背景色与网站整体风格保持一致。
    *   菜单内容应**分为多列**进行布局（建议PC端2-3列），以提高空间利用率。
    *   **必须包含“分类标题”** (如: `奇幻`、`科幻`)，作为不可点击的视觉分隔符，字体应加粗或有特殊样式。

*   **4.3 导航项元素 (每条链接):**
    *   **图标 (Icon):** 每个生成器链接的左侧必须有一个独特且高辨识度的SVG图标（如：精灵头像、飞船、城堡）。图标风格需保持全站统一。
    *   **生成器名称 (Name):** 链接的主要文本，字体清晰、醒目。点击后跳转至对应的精品工具页。
    *   **描述 (Description):** 在名称下方，必须有一行**简短（不超过15个词）、吸引人**的描述文字，用稍小的、较浅的字体颜色展示。
    *   **角标 (Badge - 可选):** 可以在特定项的右上角添加`新品 (New)`或`热门 (Hot)`的角标，以引导用户。

*   **4.4 内容管理 (MVP阶段):**
    *   整个超级菜单的结构和内容（包括分类、链接、图标、描述）**应在前端代码中硬编码 (hardcoded)**。
    *   应创建一个专门的配置文件（如 `config/navigation.ts`）来统一管理这些数据，方便开发人员修改和维护。

*   **4.5 交互与动画:**
    *   菜单的展开和收起应有**平滑、快速的过渡动画**（如：淡入淡出+轻微向下滑动），时长建议为 0.2-0.3 秒。
    *   当鼠标悬停在菜单内的任何一个具体链接上时，该链接项应有**清晰的悬停状态**（如：背景色变亮、文字颜色变化）。

**5. 验收标准:**
*   [ ] 鼠标悬停在“生成器”上时，超级菜单能正常展开。
*   [ ] 菜单布局在主流PC分辨率下（如1920x1080, 1440x900）显示正常，不会出现折行或错位。
*   [ ] 每个导航项都正确显示了图标、名称和描述。
*   [ ] 点击任何一个导航项，都能正确跳转到对应的URL。
*   [ ] 在移动端，菜单能正确地在汉堡菜单中展开，并适配屏幕宽度。
*   [ ] 所有链接对于SEO爬虫都是可见和可抓取的标准`<a>`标签。

**6. SEO 考量:**

*   确保所有链接最终都渲染为标准的 `<a href="...">` 标签，以便爬虫能够抓取。
*   在Next.js中，使用 `<Link href="...">` 组件会自动处理这个问题。
