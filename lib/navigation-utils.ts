import { NavigationGenerator } from "@/types/navigation";

/**
 * 按指定字段对数组进行分组
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const group = String(item[key]);
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

/**
 * 获取角标的样式变体
 */
export function getBadgeVariant(badge: string): "default" | "secondary" | "destructive" | "outline" {
  switch (badge) {
    case 'hot':
      return 'destructive';
    case 'new':
      return 'default';
    case 'popular':
      return 'secondary';
    default:
      return 'outline';
  }
}

/**
 * 获取角标的显示文本
 */
export function getBadgeText(badge: string): string {
  switch (badge) {
    case 'hot':
      return 'Hot';
    case 'new':
      return 'New';
    case 'popular':
      return 'Popular';
    default:
      return badge;
  }
}
